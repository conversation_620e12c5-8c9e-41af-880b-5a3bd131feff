# PIB-METHOD Operational Guidelines

This document provides comprehensive operational guidelines for all PIB agents and development workflows.

## MCP Tool Integration

### Research Tools
- **Perplexity MCP**: Primary tool for external research and market analysis
  - Real-time web search with AI-powered analysis and synthesis
  - Market research, technology trends, and competitive intelligence
  - Industry best practices and emerging methodologies research
  - Validation of market assumptions and business hypotheses

- **Firecrawl MCP**: Web content extraction and competitive analysis
  - Extract content from competitor websites and industry resources
  - Gather scattered information from multiple web sources
  - Competitive analysis and market intelligence gathering
  - Content extraction for comprehensive research compilation

- **Context7 MCP**: Library and framework documentation lookup
  - Up-to-date library and framework documentation retrieval
  - API reference access and implementation guides
  - Technology research and feasibility assessment
  - Just-in-time documentation during development

### Development Tools
- **Built-in Analysis Tools**: Comprehensive development assistance
  - Use `*analyze` for comprehensive codebase assessment and technical debt analysis
  - Use `*codereview` for thorough code analysis with severity levels
  - Use `*debug` for systematic root cause analysis of complex issues
  - Use `*refactor` for code improvement recommendations
  - Use `*testgen` for comprehensive test generation and coverage analysis
  - Use `*secaudit` for security vulnerability assessment
  - Use `*docgen` for automated documentation generation

- **Context7 MCP**: Implementation documentation and pattern research
  - Just-in-time API documentation lookup during coding
  - Framework best practices and implementation patterns
  - Library integration guides and dependency research
  - Support for LEVER framework compliance (leverage existing patterns)

### Testing Tools
- **Playwright MCP**: Browser automation and end-to-end testing
  - Web application testing and user flow validation
  - Cross-browser compatibility testing and automation
  - Screenshot and visual regression testing
  - Performance and accessibility testing automation

- **Built-in Testing Tools**: Test strategy and quality analysis
  - Use `*testgen` for comprehensive test case generation
  - Use `analyze` for test coverage analysis and quality assessment
  - Use `debug` for test failure investigation and root cause analysis
  - Use `chat` for test strategy brainstorming and methodology discussions

### MCP Usage Protocols

#### For Research Tasks (Analyst Agent)
1. **Primary Research Workflow**: Perplexity (broad research) → Firecrawl (specific data) → Context7 (technical details) → Zen (validation)
2. **Source Validation**: Always use multiple MCPs for critical findings validation
3. **Current Information Priority**: Use Perplexity for real-time market conditions and trends
4. **Documentation**: Reference specific MCP sources in all research outputs

#### For Development Tasks (Dev Agent)
1. **Pre-Implementation**: Context7 (pattern research) → Zen analyze (duplication check) → LEVER compliance validation
2. **During Development**: Context7 (API documentation) → Zen debug (issue resolution) → quality validation
3. **Code Quality**: Zen codereview (multi-model validation) → PIB standards compliance
4. **Pre-Commit**: Zen precommit (architectural consistency) → commit validation

#### For Architecture Tasks (Architect Agent)
1. **Technology Selection**: Context7 (research options) → Zen thinkdeep (multi-model validation) → decision rationale
2. **Architecture Design**: Context7 (best practices) → Zen thinkdeep (complex decisions) → design validation
3. **Risk Assessment**: Multi-MCP research → Zen multi-model analysis → comprehensive risk identification

#### For Testing Tasks (QA Tester Agent)
1. **Test Planning**: Zen testgen (strategy generation) → Playwright (implementation feasibility) → test plan creation
2. **Test Execution**: Playwright (browser automation) → result collection → quality validation
3. **Issue Investigation**: Zen debug (root cause analysis) → resolution planning → validation testing

## LEVER Framework Integration with MCPs

### Leverage (L) - Use Existing Solutions
- **Context7**: Research existing libraries, patterns, and proven solutions
- **Perplexity**: Find industry-standard approaches and successful implementations
- ***analyze**: Identify reusable components and patterns in current codebase
- **Protocol**: Always check existing solutions before creating new implementations

### Extend (E) - Build Upon Existing
- **Context7**: Research extension possibilities and API compatibility
- ***thinkdeep**: Analyze extension architecture and design implications
- **Perplexity**: Find examples of successful extensions and integration patterns
- **Protocol**: Prefer extending existing systems over creating parallel solutions

### Verify (V) - Validate Through Testing
- ***codereview**: Comprehensive code validation and quality assessment
- **Playwright**: Automated testing and user flow verification
- ***testgen**: Comprehensive test coverage and scenario validation
- **Protocol**: All implementations must pass multi-model validation

### Eliminate (E) - Remove Duplication
- ***analyze**: Detect code duplication, redundancy, and unnecessary complexity
- **Context7**: Research consolidation patterns and refactoring approaches
- ***debug**: Identify and eliminate unnecessary system complexity
- **Protocol**: Actively remove duplication and consolidate similar functionality

### Reduce (R) - Simplify Implementation
- **Brainstorming**: Consider simplification approaches and alternative solutions
- **Context7**: Find simpler implementation patterns and minimal approaches
- ***thinkdeep**: Analyze complexity reduction strategies and trade-offs
- **Protocol**: Always choose the simplest solution that meets requirements

## Quality Assurance Framework

### Multi-MCP Validation Requirements
For critical decisions requiring high confidence, use multiple MCP validation:

1. **Research Validation**: Require agreement between Perplexity + Context7 + comprehensive analysis
2. **Technical Decisions**: Require Context7 documentation + multi-perspective consensus + practical validation
3. **Architecture Decisions**: Require Context7 patterns + deep validation + risk assessment
4. **Implementation Quality**: Require *codereview + Context7 best practices + LEVER compliance

### Quality Gates
- **Before Task Assignment**: Dependencies verified, appropriate MCPs identified, agents available
- **During Implementation**: Continuous MCP-enhanced validation and quality checking
- **Before Task Completion**: Multi-MCP validation, quality standards met, documentation complete
- **Before Integration**: Cross-agent validation, integration testing, quality gate approval

### MCP Quality Standards
- **Source Attribution**: Always reference which MCP provided specific information
- **Cross-Validation**: Use multiple MCPs for critical findings and decisions
- **Current Information**: Prioritize real-time sources (Perplexity) for market conditions
- **Technical Accuracy**: Use Context7 for precise technical documentation and standards
- **Comprehensive Analysis**: Use built-in tools for complex analysis requiring multiple perspectives

## Documentation and Knowledge Management

### MCP Documentation Requirements
- **Research Attribution**: Document which MCPs were used for research and validation
- **Decision Rationale**: Include MCP-derived insights in decision documentation
- **Quality Metrics**: Track MCP usage effectiveness and validation success rates
- **Knowledge Updates**: Feed MCP insights back into agent knowledge base

### Documentation Standards
- **Multi-Source Validation**: Document when findings were validated across multiple MCPs
- **Confidence Levels**: Indicate confidence based on MCP validation coverage
- **Source Quality**: Reference authoritative sources found through MCP research
- **Update Tracking**: Monitor when MCP-derived information needs refreshing

## Emergency Procedures

### When MCP Tools Conflict
1. **Immediate Assessment**: Identify specific areas of conflict between MCP recommendations
2. **Source Evaluation**: Assess recency, authority, and context of conflicting information
3. **Multi-Perspective Validation**: Use built-in tools for objective conflict resolution analysis
4. **User Escalation**: Report conflicts that cannot be resolved through technical validation

### When MCP Tools Are Unavailable
1. **Graceful Degradation**: Continue with available tools and clearly note limitations
2. **Alternative Methods**: Use traditional research and analysis methods where possible
3. **Quality Adjustment**: Adjust confidence levels and validation requirements accordingly
4. **Clear Documentation**: Note which MCPs were unavailable for transparency and future reference

### When Quality Standards Cannot Be Met
1. **Risk Assessment**: Evaluate impact of proceeding with reduced MCP validation
2. **User Communication**: Clearly communicate quality trade-offs and implications
3. **Mitigation Planning**: Develop plans to address quality gaps in future iterations
4. **Documentation**: Document known limitations and their potential impact on outcomes

## Success Metrics and Continuous Improvement

### MCP Integration Effectiveness
- **Coverage**: Percentage of tasks using appropriate MCP validation
- **Quality**: Reduction in rework due to better initial research and validation
- **Speed**: Time savings from efficient MCP tool usage and parallel research
- **Accuracy**: Improved decision quality through multi-source validation

### Agent Performance Enhancement
- **Research Quality**: Comprehensiveness and accuracy of MCP-enhanced research
- **Development Efficiency**: Faster implementation through better pattern research
- **Test Coverage**: More comprehensive testing through MCP-enhanced test generation
- **Architecture Quality**: Better decisions through multi-model MCP validation

### Continuous Improvement Process
- **Regular Review**: Assess MCP usage patterns and effectiveness
- **Tool Optimization**: Refine MCP usage protocols based on outcomes
- **Knowledge Updates**: Incorporate MCP insights into agent training and guidelines
- **Quality Enhancement**: Continuously improve validation processes and standards

This framework ensures that all PIB agents leverage MCP capabilities effectively while maintaining high quality standards and efficient workflows.
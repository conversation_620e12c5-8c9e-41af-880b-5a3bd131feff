# MCP Capabilities Matrix

This document provides a comprehensive reference for PIB agents on available MCP (Model Context Protocol) tools and their optimal usage patterns.

## Available MCP Tools Overview

| MCP Tool | Type | Primary Use Cases | Key Capabilities |
|----------|------|------------------|------------------|
| **Context7** | Documentation | Library docs, API references | Real-time documentation retrieval, framework guides |
| **Perplexity** | Research | Market research, trends, competitive analysis | AI-powered search, real-time web analysis |
| **Firecrawl** | Content Extraction | Web scraping, competitive intelligence | Structured content extraction, data gathering |
| **Playwright** | Testing | Browser automation, E2E testing | Web testing, screenshots, user flow automation |
| **Firebase** | Backend Services | Authentication, database, cloud functions | Firebase SDK integration, real-time updates |

## Agent-to-MCP Mapping

### Architect Agent
| **Primary MCPs** | **Use Cases** | **Integration Points** |
|------------------|---------------|------------------------|
| **Context7** (Primary) | Technology research, framework documentation | API research, architectural patterns, best practices |
| **Perplexity** | Industry trends, architectural approaches | Market validation, trend analysis |

**Workflow Integration:**
- **Technology Selection**: Context7 → research options → validation through analysis
- **Architecture Design**: Context7 → best practices → informed design decisions
- **Risk Assessment**: Comprehensive analysis → risk identification and mitigation

### Analyst Agent (Research Focus)
| **Primary MCPs** | **Use Cases** | **Integration Points** |
|------------------|---------------|------------------------|
| **Perplexity** (Primary) | Market research, industry trends, competitive intelligence | External knowledge gathering, market validation |
| **Firecrawl** | Competitive analysis, content extraction | Competitor data, industry resources |
| **Context7** | Technical research, feasibility assessment | Technology trends, framework analysis |

**Workflow Integration:**
- **Primary Research**: Perplexity → broad market analysis → trend identification
- **Competitive Analysis**: Firecrawl → competitor data → Perplexity → market context
- **Technical Research**: Context7 → framework docs → feasibility validation
- **Research Validation**: Multi-MCP findings → cross-validation and synthesis

### Developer Agent
| **Primary MCPs** | **Use Cases** | **Integration Points** |
|------------------|---------------|------------------------|
| **Context7** (Primary) | Just-in-time documentation, API references | Implementation guidance, pattern research |
| **Built-in Tools** | Code analysis, debugging, quality validation | Development workflow, code review, issue resolution |

**Workflow Integration:**
- **Before Implementation**: Context7 → research patterns → LEVER compliance check
- **During Development**: Context7 → API docs → debugging → issue resolution
- **Code Quality**: Code review → validation → PIB standards compliance
- **Before Commits**: Pre-commit checks → architectural consistency validation

### QA Tester Agent
| **Primary MCPs** | **Use Cases** | **Integration Points** |
|------------------|---------------|------------------------|
| **Playwright** (Primary) | Browser automation, E2E testing, visual testing | Web application testing, user flow validation |
| **Built-in Tools** | Test strategy, coverage analysis, failure investigation | Test generation, quality assessment, debugging |

**Workflow Integration:**
- **Test Planning**: Test generation → comprehensive test cases → Playwright implementation
- **Test Execution**: Playwright → browser automation → result collection
- **Failure Analysis**: Debug tools → root cause analysis → resolution planning
- **Quality Assessment**: Analysis → coverage metrics → improvement recommendations

### Platform Engineer Agent
| **Primary MCPs** | **Use Cases** | **Integration Points** |
|------------------|---------------|------------------------|
| **Firebase** | Backend services, authentication, database | Infrastructure setup, service configuration |
| **Context7** | Infrastructure patterns, deployment docs | Best practices, configuration guidance |

**Workflow Integration:**
- **Infrastructure Setup**: Firebase → service configuration → deployment
- **Pattern Research**: Context7 → infrastructure patterns → implementation
- **Monitoring**: Firebase analytics → performance tracking → optimization

## MCP Usage Protocols

### Research Workflows
1. **Broad Market Research**: Perplexity (primary) → Firecrawl (specific data) → validation
2. **Technical Research**: Context7 (documentation) → Perplexity (trends) → analysis
3. **Competitive Analysis**: Firecrawl (data extraction) → Perplexity (context) → synthesis

### Development Workflows  
1. **Pattern Research**: Context7 (existing patterns) → duplication check
2. **Implementation**: Context7 (API docs) → debugging (issue resolution)
3. **Quality Assurance**: Code review (validation) → pre-commit (consistency)

### Testing Workflows
1. **Test Design**: Test generation (strategy) → Playwright (implementation)
2. **Test Execution**: Playwright (automation) → analysis (coverage)
3. **Issue Resolution**: Debug (analysis) → Playwright (validation)

### Architecture Workflows
1. **Technology Selection**: Context7 (research) → deep analysis (validation)
2. **Design Validation**: Impact analysis → brainstorming → decision making
3. **Risk Assessment**: Multi-MCP research → comprehensive analysis

## LEVER Framework Integration

### Leverage (L)
- **Context7**: Find existing libraries, patterns, and solutions
- **Perplexity**: Research proven approaches and industry standards
- **Built-in analysis**: Identify reusable components in current codebase

### Extend (E) 
- **Context7**: Research extension possibilities and compatibility
- **Deep analysis**: Analyze extension architecture and design
- **Perplexity**: Find examples of successful extensions

### Verify (V)
- **Code review**: Comprehensive code validation
- **Playwright**: Automated testing and verification
- **Test generation**: Comprehensive test coverage validation

### Eliminate (E)
- **Code analysis**: Detect code duplication and redundancy
- **Context7**: Research consolidation patterns
- **Debug tools**: Identify unnecessary complexity

### Reduce (R)
- **Brainstorming**: Simplification approaches
- **Context7**: Find simpler implementation patterns
- **Deep analysis**: Analyze complexity reduction strategies

## Quality Gates and Validation

### Multi-MCP Validation Requirements
For critical decisions requiring high confidence:
1. **Research Validation**: Require agreement between Perplexity + Context7 + analysis
2. **Technical Decisions**: Require Context7 documentation + multi-perspective consensus
3. **Architecture Decisions**: Require Context7 patterns + deep validation
4. **Test Strategy**: Require test generation + Playwright feasibility confirmation

### MCP Quality Standards
- **Documentation Currency**: Context7 results must be from current version
- **Research Recency**: Perplexity results should be within last 6 months
- **Content Accuracy**: Firecrawl data must be validated against multiple sources
- **Test Coverage**: Playwright tests must cover critical user journeys

## Conflict Resolution Protocol

When MCP tools provide conflicting information:
1. **Immediate Assessment**: Identify specific areas of conflict between MCP recommendations
2. **Source Evaluation**: Assess recency and authority of conflicting information
3. **Multi-Perspective Validation**: Use multiple tools for objective conflict resolution
4. **User Escalation**: Report conflicts that cannot be resolved through validation

## Integration Best Practices

### For Research Tasks
- **Start Broad**: Use Perplexity for initial exploration
- **Go Deep**: Use Context7 for technical specifics
- **Extract Details**: Use Firecrawl for specific competitor/industry data
- **Validate Findings**: Cross-reference across multiple MCPs

### For Architecture Design
- **Pattern First**: Always check Context7 for existing architectural patterns
- **Market Validation**: Use Perplexity to validate architectural trends
- **Risk Analysis**: Combine multiple perspectives for comprehensive risk assessment
- **Decision Documentation**: Document MCP findings that influenced decisions

### For Development Tasks
- **Documentation First**: Always check Context7 before implementing
- **Continuous Validation**: Use analysis tools throughout development
- **Quality Checks**: Regular code review and validation
- **Test Integration**: Combine test generation with Playwright validation

### For Technical Implementation
- **Pattern-First Approach**: Always check Context7 for existing patterns before creating new code
- **Continuous Validation**: Use available tools throughout development for quality assurance
- **Integration Testing**: Combine Context7 research with Playwright validation

## MCP Tool Selection Matrix

| Task Type | Primary MCP | Secondary MCP | Validation MCP |
|-----------|-------------|---------------|----------------|
| Market Research | Perplexity | Firecrawl | Context7 |
| Technical Docs | Context7 | Perplexity | Documentation |
| Competitive Analysis | Firecrawl | Perplexity | Multi-source |
| API Integration | Context7 | Documentation | Testing |
| Test Automation | Playwright | Test Gen | Coverage Analysis |
| Architecture Design | Context7 | Perplexity | Deep Analysis |
| Code Quality | Built-in Tools | Context7 | Review Process |
| Debugging | Built-in Debug | Context7 | Root Cause Analysis |

## Continuous Improvement

The MCP ecosystem is continuously evolving. Agents should:
1. **Stay Updated**: Regularly check for new MCP capabilities
2. **Share Learnings**: Document effective MCP usage patterns
3. **Optimize Workflows**: Continuously refine MCP integration strategies
4. **Measure Impact**: Track productivity improvements from MCP usage

Remember: MCPs are tools to enhance agent capabilities, not replace agent judgment. Always apply critical thinking and PIB Method principles when using MCP recommendations.
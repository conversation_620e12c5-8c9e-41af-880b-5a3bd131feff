{"name": "turbois", "version": "1.0.0", "description": "TurboIs - Visual Flow Builder with Desktop and Web Support", "main": "src/frontend/build/index.html", "scripts": {"web": "uv run langflow run", "dev": "npm run kill-ports && concurrently \"LANGFLOW_SKIP_MIGRATIONS=true uv run langflow run\" \"cd src/frontend && npm start\"", "desktop": "npm run kill-ports && concurrently \"LANGFLOW_SKIP_MIGRATIONS=true uv run langflow run\" \"cd src/frontend && npm run electron:dev\"", "kill-ports": "lsof -ti:3000 | xargs kill -9 2>/dev/null || true && lsof -ti:7860 | xargs kill -9 2>/dev/null || true", "desktop:build": "cd src/frontend && npm run build:desktop && npm run electron:pack", "desktop:dist": "cd src/frontend && npm run build:desktop && npm run electron:dist", "install:frontend": "cd src/frontend && npm install", "install:electron": "cd electron && npm install", "install:all": "npm run install:frontend && npm run install:electron", "test:desktop": "cd electron && npm test", "build:web": "cd src/frontend && npm run build:web", "build:desktop": "cd src/frontend && npm run build:desktop", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:debug": "playwright test --debug", "test:e2e:headed": "playwright test --headed", "test:e2e:chrome": "playwright test --project=chromium", "test:e2e:firefox": "playwright test --project=firefox", "test:e2e:webkit": "playwright test --project=webkit", "test:e2e:mobile": "playwright test --project='Mobile Chrome' --project='Mobile Safari'", "test:e2e:report": "playwright show-report", "test:e2e:codegen": "playwright codegen", "test:e2e:install": "playwright install --with-deps"}, "keywords": ["turbois", "langflow", "electron", "desktop", "web", "flow-builder", "visual-programming"], "author": "TurboIs Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/PMStander/langflow.git"}, "engines": {"node": ">=16.0.0"}, "dependencies": {"mitt": "^3.0.1", "playwright": "^1.54.1"}, "devDependencies": {"@axe-core/playwright": "^4.8.0", "@types/node": "^20.14.2", "canvas": "^2.11.2", "concurrently": "^9.2.0", "pixelmatch": "^5.3.0", "pngjs": "^7.0.0", "typescript": "^5.4.5"}}
# Create Technology-Specific Agent

Create a new specialized agent with research-based expertise for specific technologies or frameworks.

## Usage
```bash
/workflows:create-tech-agent "<technology>" [--research] [--version <version>]
```

## Examples
```bash
# Create React specialist with latest research
/workflows:create-tech-agent "React" --research --version 18

# Create PostgreSQL expert
/workflows:create-tech-agent "PostgreSQL" --research

# Create specialized hook expert
/workflows:create-tech-agent "React Hooks" --research
```

## Workflow Process

### Phase 1: Research (if --research)
1. **Gather Documentation**
   - Use Perplexity to find official docs
   - Research best practices guides
   - Find common patterns and anti-patterns
   - Identify security considerations

2. **Analyze Framework Ecosystem**
   - Popular libraries and tools
   - Testing frameworks
   - Development workflows
   - Community standards

3. **Study Real-World Usage**
   - Common implementation patterns
   - Performance optimization techniques
   - Error handling approaches
   - Integration strategies

### Phase 2: Agent Generation
4. **Create Agent Definition**
   - Use tech-specialist-template.md
   - Insert researched knowledge
   - Define expertise areas
   - Set up delegation patterns

5. **Define Interaction Patterns**
   - When to invoke this agent
   - Handoff mechanisms
   - Collaboration with other agents
   - Review requirements

6. **Add Framework-Specific Examples**
   - Code patterns
   - Common tasks
   - Best practices
   - Anti-pattern recognition

### Phase 3: Validation
7. **Test Agent Knowledge**
   - Verify accuracy of patterns
   - Check against documentation
   - Validate with examples
   - Ensure LEVER compliance

8. **Integration Testing**
   - Test handoffs to other agents
   - Verify routing rules
   - Check collaboration patterns
   - Validate quality gates

## Research Process Example

```markdown
## Research: React 18 Specialist

### Official Documentation
- Concurrent features (Suspense, startTransition)
- New hooks (useId, useSyncExternalStore)
- Automatic batching improvements

### Best Practices (2024)
- Server Components adoption
- Streaming SSR patterns
- Error boundary strategies
- Performance optimization with memo

### Common Patterns
- Custom hook composition
- Context optimization
- Lazy loading strategies
- Type-safe component props

### Anti-Patterns to Avoid
- useEffect overuse
- State lifting mistakes
- Re-render cascades
- Memory leaks in effects
```

## Generated Agent Structure

```
.claude/agents/tech-specialists/frontend/react-specialist.md
├── Metadata (name, tools, examples)
├── Core Expertise
├── Research-Based Knowledge
├── Framework Patterns
├── Delegation Rules
├── Integration Points
└── Quality Standards
```

## Parameters
- `<technology>`: Name of the technology/framework
- `--research`: Perform online research for latest practices
- `--version`: Specific version to specialize in
- `--update`: Update existing agent with new research

## Quality Assurance
- [ ] Research from authoritative sources
- [ ] Include version-specific features
- [ ] Cover security best practices
- [ ] Add performance guidelines
- [ ] Include testing strategies
- [ ] Define clear boundaries
- [ ] Set up proper delegations

## Integration Features

### Auto-Registration
Created agents automatically:
- Register in agent index
- Update routing rules
- Add to project detection
- Include in orchestration
- Add to `.claude/project-agents.json` for Task tool integration

### Collaboration Setup
- Define handoff patterns
- Set review requirements
- Create consultation rules
- Establish quality gates

## Output Example

```bash
> /workflows:create-tech-agent "React" --research --version 18

[Researching React 18...]
✓ Found 15 best practice guides
✓ Analyzed 8 official documentation pages
✓ Identified 12 common patterns
✓ Discovered 5 performance techniques

[Creating Agent...]
✓ Generated react-specialist.md
✓ Added 23 code examples
✓ Defined 8 delegation patterns
✓ Set up 5 integration points

[Validating...]
✓ Verified against React 18 docs
✓ Tested pattern accuracy
✓ Validated LEVER compliance

Agent created: .claude/agents/tech-specialists/frontend/react-specialist.md

[Registration]
✓ Added to .claude/project-agents.json
✓ Available as subagent_type: "react-specialist"

Ready for use in projects!

Usage:
Task({
  description: "Implement React component",
  prompt: "Create a new dashboard component...",
  subagent_type: "react-specialist"
})
```

## Related Commands
- `/workflows:project-init-tech` - Initialize with tech detection
- `/agents:tech-lead` - Orchestrate specialist agents
- `/core:agents` - List all available agents
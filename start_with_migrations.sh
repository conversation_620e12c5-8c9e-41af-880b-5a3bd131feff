#!/bin/bash

# Kill any processes on ports 3000 and 7860
echo "🔄 Killing processes on ports 3000 and 7860..."
lsof -ti:3000 | xargs kill -9 2>/dev/null || true
lsof -ti:7860 | xargs kill -9 2>/dev/null || true

# Remove old database to start fresh
echo "🗑️  Removing old database..."
rm -f langflow.db
rm -f src/backend/base/langflow/langflow.db

# Start services with migrations enabled
echo "🚀 Starting Langflow with database migrations..."
concurrently \
  "uv run langflow run --host 0.0.0.0 --port 7860" \
  "cd src/frontend && npm start"

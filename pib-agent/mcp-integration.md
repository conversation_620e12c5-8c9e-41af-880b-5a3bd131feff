# MCP Integration Guide for PIB Agents

## Overview
This guide documents how PIB agents leverage MCP (Model Context Protocol) tools to enhance their capabilities with AI-powered analysis, testing, and decision-making.

## Available MCP Tools

### Playwright MCP (Web Testing)
To install: `npx @mcpintegrations/install playwright`

Provides browser automation for:
- **`*mcp-playwright navigate [url]`** - Navigate to web pages
- **`*mcp-playwright click [selector]`** - Click elements
- **`*mcp-playwright fill [selector] [value]`** - Fill form fields
- **`*mcp-playwright screenshot [name]`** - Capture screenshots
- **`*mcp-playwright assert [condition]`** - Validate behavior

## Agent-Specific MCP Usage

### Bill (PM/Orchestrator)
Primary tools for complex orchestration:
```markdown
*bill orchestrate "implement authentication"
  → Uses: Context7 for documentation research
  → Uses: Perplexity for best practices analysis
  → Uses: Firecrawl for competitive analysis
```

### Architect
Architecture and design validation:
```markdown
*architect design "microservices migration"  
  → Uses: Context7 for architectural patterns
  → Uses: Perplexity for industry trends
  → Uses: Built-in analysis tools for assessment
```

### Dev
Implementation and debugging:
```markdown
*dev implement "payment processing"
  → Uses: Context7 for API documentation
  → Uses: Built-in debugging tools
  → Uses: Code analysis for improvement
```

### QA/Tester
Comprehensive testing:
```markdown
*qa test "checkout flow"
  → Uses: Playwright MCP for UI testing
  → Uses: Built-in test generation
  → Uses: Code review for test quality
```

### Code Reviewer
Multi-perspective analysis:
```markdown
*reviewer check "PR #123"
  → Uses: Built-in code review capabilities
  → Uses: Security audit tools
  → Uses: Team consensus for approval decisions
```

## Workflow Integration Examples

### Enhanced Module Development
```markdown
# Original workflow
*dev implement → *reviewer check → *changer update

# MCP-enhanced workflow  
*dev implement
  → Context7 research before implementation
  → Built-in debugging during development
  → Test generation for test creation
*reviewer check
  → Comprehensive code review
  → Security validation
*changer update
  → Improvement analysis and implementation
```

### Complex Problem Solving
```markdown
# Bill encounters complex requirement
*bill: "This needs deep analysis..."
Using available tools for "user authentication with SSO, MFA, and social login":
  → Step 1: Analyze requirements and constraints
  → Step 2: Research existing patterns with Context7
  → Step 3: Design architecture approach
  → Step 4: Identify risks and mitigations
  → Step 5: Create implementation plan
  → Validation and recommendations
```

### Web Application Testing
```markdown
# QA testing e-commerce checkout
*qa: "Testing checkout flow with Playwright..."
playwright_navigate "https://store.example.com"
playwright_click "a[href='/products']"
playwright_fill "#search" "laptop"
playwright_click ".add-to-cart"
playwright_navigate "/checkout"
playwright_fill "#email" "<EMAIL>"
playwright_screenshot "checkout-form"
playwright_click "button[type='submit']"
playwright_assert "text=Order Confirmed"
```

## Session Logging

MCP sessions are automatically logged for traceability:

```
.ai/mcp-sessions/
├── 2024-06-24-analysis-auth/
│   ├── session.json
│   ├── step-1-analysis.md
│   ├── step-2-findings.md
│   ├── step-3-design.md
│   ├── step-4-risks.md
│   ├── step-5-plan.md
│   └── recommendations.md
├── 2024-06-24-playwright-checkout/
│   ├── test-results.json
│   ├── screenshots/
│   │   ├── checkout-form.png
│   │   └── order-confirmed.png
│   └── report.html
└── 2024-06-24-review-pr123/
    ├── review-summary.md
    ├── issues-found.json
    └── recommendations.md
```

## Best Practices

1. **Use MCP for Complex Tasks**: Simple tasks don't need MCP overhead
2. **Leverage Multiple Tools**: For comprehensive analysis
3. **Document MCP Sessions**: Keep session logs for future reference
4. **Combine Tools**: Use multiple MCP tools in sequence for best results
5. **Agent Specialization**: Each agent should master their relevant MCP tools

## Quick Reference

| Task | Tool | Best For |
|------|---------|-----------|
| Documentation Research | Context7 | All agents |
| Market Research | Perplexity | Analyst, Bill |
| Competitive Analysis | Firecrawl | Analyst |
| Web Testing | Playwright | QA |
| Code Analysis | Built-in tools | Architect, Dev |
| Debugging | Built-in tools | Dev |
| Code Review | Built-in tools | Reviewer |
| Test Generation | Built-in tools | QA, Dev |
| Security Check | Built-in tools | Architect, Reviewer |

## Getting Help

- Check available MCP tools with appropriate commands
- Consult documentation for specific MCP usage
- Use team collaboration for complex decisions
/**
 * BE-004 BookEditor Store Constants
 * 
 * Constants and configuration values for the BookEditor store system.
 * Follows LEVER principles by leveraging existing patterns from PhotoEditor
 * while adding book-specific constants for typography, validation, and KDP compliance.
 */

/**
 * Book Size Constants (matching BookCanvas)
 */
export const BOOK_SIZES = {
  "6x9": { width: 1800, height: 2700, name: "6×9 inches" },
  "5.5x8.5": { width: 1650, height: 2550, name: "5.5×8.5 inches" },
  "5x8": { width: 1500, height: 2400, name: "5×8 inches" },
  "8.5x11": { width: 2550, height: 3300, name: "8.5×11 inches" },
  "7x10": { width: 2100, height: 3000, name: "7×10 inches" },
  "8x10": { width: 2400, height: 3000, name: "8×10 inches" },
} as const;

/**
 * Print Margins for Different Book Sizes
 */
export const PRINT_MARGINS = {
  STANDARD: {
    top: 75,    // 0.25 inches at 300 DPI
    bottom: 75,
    left: 75,
    right: 75,
  },
  CONSERVATIVE: {
    top: 90,    // 0.3 inches at 300 DPI
    bottom: 90,
    left: 90,
    right: 90,
  },
  BLEED: {
    top: 9,     // 0.03 inches at 300 DPI
    bottom: 9,
    left: 9,
    right: 9,
  },
} as const;

/**
 * Typography Constants
 */
export const TYPOGRAPHY = {
  DEFAULT_FONTS: [
    'Times New Roman',
    'Georgia',
    'Garamond',
    'Minion Pro',
    'Adobe Caslon Pro',
    'Baskerville',
    'Charter',
    'Source Serif Pro',
  ],
  HEADING_FONTS: [
    'Arial',
    'Helvetica',
    'Futura',
    'Optima',
    'Gill Sans',
    'Source Sans Pro',
  ],
  DEFAULT_SIZES: {
    BODY: 11,
    HEADING_1: 18,
    HEADING_2: 16,
    HEADING_3: 14,
    CAPTION: 9,
    FOOTNOTE: 8,
  },
  LINE_HEIGHT: {
    TIGHT: 1.2,
    NORMAL: 1.4,
    LOOSE: 1.6,
  },
} as const;

/**
 * Page Type Constants
 */
export const PAGE_TYPES = {
  TITLE: 'title',
  CHAPTER: 'chapter',
  CONTENT: 'content',
  BLANK: 'blank',
  TOC: 'toc',
  INDEX: 'index',
} as const;

/**
 * Export Format Constants
 */
export const EXPORT_FORMATS = {
  PDF: 'pdf',
  PNG: 'png',
  JPG: 'jpg',
  SVG: 'svg',
} as const;

/**
 * Validation Constants for KDP Compliance
 */
export const KDP_VALIDATION = {
  MIN_PAGES: 24,
  MAX_FILE_SIZE: 650 * 1024 * 1024, // 650MB in bytes
  MIN_DPI: 300,
  MAX_DPI: 600,
  REQUIRED_COLOR_SPACE: 'RGB',
  ACCEPTED_IMAGE_FORMATS: ['jpg', 'jpeg', 'png', 'tiff', 'pdf'],
  MIN_MARGIN: 0.25, // inches
  MAX_BLEED: 0.125, // inches
} as const;

/**
 * Performance Constants
 */
export const PERFORMANCE = {
  MAX_HISTORY_SIZE: 50,
  MAX_PAGES: 1000,
  MEMORY_CLEANUP_THRESHOLD: 100, // MB
  AUTO_SAVE_INTERVAL: 30000, // 30 seconds
  DEBOUNCE_DELAY: 300, // ms
} as const;

/**
 * Default Book Configuration
 */
export const DEFAULT_BOOK_CONFIG = {
  bookSize: 'TRADE_LARGE' as const,
  dpi: 300,
  colorMode: 'RGB' as const,
  margins: PRINT_MARGINS.STANDARD,
  bleed: PRINT_MARGINS.BLEED,
  defaultFont: {
    family: 'Times New Roman',
    size: TYPOGRAPHY.DEFAULT_SIZES.BODY,
    lineHeight: TYPOGRAPHY.LINE_HEIGHT.NORMAL,
    color: '#000000',
  },
  showGrid: false,
  showMargins: true,
  showBleed: false,
  snapToGrid: false,
  gridSize: 20,
} as const;

/**
 * Master Page Template Categories
 */
export const MASTER_PAGE_CATEGORIES = {
  HEADER: 'header',
  FOOTER: 'footer',
  LAYOUT: 'layout',
  DECORATION: 'decoration',
} as const;

/**
 * Export Quality Presets
 */
export const EXPORT_QUALITY_PRESETS = {
  DRAFT: {
    dpi: 150,
    quality: 0.7,
    compression: true,
    name: 'Draft Quality',
  },
  STANDARD: {
    dpi: 300,
    quality: 0.9,
    compression: false,
    name: 'Standard Quality',
  },
  HIGH: {
    dpi: 600,
    quality: 1.0,
    compression: false,
    name: 'High Quality',
  },
} as const;

/**
 * Tool Categories for Book Editor
 */
export const TOOL_CATEGORIES = {
  TEXT: 'text',
  IMAGE: 'image',
  SHAPE: 'shape',
  PAGE: 'page',
  MASTER: 'master',
  EXPORT: 'export',
  VALIDATION: 'validation',
} as const;

/**
 * Default Page Names by Type
 */
export const DEFAULT_PAGE_NAMES = {
  [PAGE_TYPES.TITLE]: 'Title Page',
  [PAGE_TYPES.CHAPTER]: 'Chapter',
  [PAGE_TYPES.CONTENT]: 'Page',
  [PAGE_TYPES.BLANK]: 'Blank Page',
  [PAGE_TYPES.TOC]: 'Table of Contents',
  [PAGE_TYPES.INDEX]: 'Index',
} as const;

/**
 * Color Mode Constants
 */
export const COLOR_MODES = {
  RGB: 'RGB',
  CMYK: 'CMYK',
} as const;

/**
 * Validation Severity Levels
 */
export const VALIDATION_SEVERITY = {
  CRITICAL: 'critical',
  HIGH: 'high',
  MEDIUM: 'medium',
  LOW: 'low',
} as const;

/**
 * Error Types for Book Editor
 */
export const ERROR_TYPES = {
  CANVAS: 'canvas',
  PAGE: 'page',
  TYPOGRAPHY: 'typography',
  EXPORT: 'export',
  VALIDATION: 'validation',
  MEMORY: 'memory',
  SYNC: 'sync',
} as const;

import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { analyticsApi } from '@/controllers/API/services/analytics';
import { 
  AnalyticsDashboard,
  AnalyticsReport,
  AnalyticsKPI,
  DashboardData,
  MetricTimeSeries,
  OverviewMetrics,
  PipelineAnalytics,
  GeographicDistribution
} from '@/types/analytics';

interface AnalyticsFilters {
  date_range?: {
    start: string;
    end: string;
  };
  period?: string;
  dimensions?: Record<string, any>;
}

interface AnalyticsState {
  // Dashboards
  dashboards: AnalyticsDashboard[];
  currentDashboard: AnalyticsDashboard | null;
  dashboardData: DashboardData | null;
  
  // Reports
  reports: AnalyticsReport[];
  reportTemplates: any[];
  reportExecutions: Record<string, any[]>;
  
  // KPIs
  kpis: AnalyticsKPI[];
  
  // Metrics
  overviewMetrics: OverviewMetrics | null;
  pipelineAnalytics: PipelineAnalytics | null;
  geographicDistribution: GeographicDistribution | null;
  timeSeriesData: Record<string, MetricTimeSeries[]>;
  
  // UI State
  isLoading: boolean;
  error: string | null;
  filters: AnalyticsFilters;
  
  // Actions - Dashboards
  fetchDashboards: (workspaceId: string) => Promise<void>;
  fetchDashboard: (workspaceId: string, dashboardId: string) => Promise<void>;
  fetchDefaultDashboard: (workspaceId: string) => Promise<void>;
  fetchDashboardData: (workspaceId: string, dashboardId: string, filters?: AnalyticsFilters) => Promise<void>;
  createDashboard: (workspaceId: string, data: any) => Promise<void>;
  updateDashboard: (workspaceId: string, dashboardId: string, updates: any) => Promise<void>;
  deleteDashboard: (workspaceId: string, dashboardId: string) => Promise<void>;
  
  // Actions - Reports
  fetchReports: (workspaceId: string) => Promise<void>;
  fetchReportTemplates: (workspaceId: string) => Promise<void>;
  createReport: (workspaceId: string, data: any) => Promise<void>;
  executeReport: (workspaceId: string, reportId: string, format?: string, filters?: any) => Promise<void>;
  fetchReportHistory: (workspaceId: string, reportId: string) => Promise<void>;
  scheduleReport: (workspaceId: string, reportId: string, schedule: any) => Promise<void>;
  
  // Actions - KPIs
  fetchKPIs: (workspaceId: string, includeValues?: boolean) => Promise<void>;
  createKPI: (workspaceId: string, data: any) => Promise<void>;
  updateKPI: (workspaceId: string, kpiId: string, updates: any) => Promise<void>;
  deleteKPI: (workspaceId: string, kpiId: string) => Promise<void>;
  
  // Actions - Metrics
  fetchOverviewMetrics: (workspaceId: string, dateRange?: any) => Promise<void>;
  fetchPipelineAnalytics: (workspaceId: string, dateRange?: any) => Promise<void>;
  fetchGeographicDistribution: (workspaceId: string, dateRange?: any) => Promise<void>;
  fetchTimeSeries: (
    workspaceId: string, 
    metricType: string, 
    metricName: string,
    dateRange: any,
    granularity?: string,
    dimensions?: any
  ) => Promise<void>;
  fetchComparison: (
    workspaceId: string,
    metricType: string,
    metricName: string,
    currentPeriod: any,
    comparisonPeriod: any,
    dimensions?: any
  ) => Promise<void>;
  
  // Actions - Activities
  recordActivity: (workspaceId: string, activity: any) => Promise<void>;
  recordMetric: (workspaceId: string, metric: any) => Promise<void>;
  
  // UI Actions
  setFilters: (filters: AnalyticsFilters) => void;
  setCurrentDashboard: (dashboard: AnalyticsDashboard | null) => void;
  clearError: () => void;
}

export const useAnalyticsStore = create<AnalyticsState>()(
  devtools(
    (set, get) => ({
      // Initial state
      dashboards: [],
      currentDashboard: null,
      dashboardData: null,
      reports: [],
      reportTemplates: [],
      reportExecutions: {},
      kpis: [],
      overviewMetrics: null,
      pipelineAnalytics: null,
      geographicDistribution: null,
      timeSeriesData: {},
      isLoading: false,
      error: null,
      filters: {},

      // Dashboard actions
      fetchDashboards: async (workspaceId: string) => {
        set({ isLoading: true, error: null });
        try {
          const dashboards = await analyticsApi.listDashboards(workspaceId);
          set({ dashboards, isLoading: false });
        } catch (error: any) {
          set({ error: error.message, isLoading: false });
        }
      },

      fetchDashboard: async (workspaceId: string, dashboardId: string) => {
        set({ isLoading: true, error: null });
        try {
          const dashboard = await analyticsApi.getDashboard(workspaceId, dashboardId);
          set({ currentDashboard: dashboard, isLoading: false });
        } catch (error: any) {
          set({ error: error.message, isLoading: false });
        }
      },

      fetchDefaultDashboard: async (workspaceId: string) => {
        set({ isLoading: true, error: null });
        try {
          const dashboard = await analyticsApi.getDefaultDashboard(workspaceId);
          set({ currentDashboard: dashboard, isLoading: false });
        } catch (error: any) {
          set({ error: error.message, isLoading: false });
        }
      },

      fetchDashboardData: async (workspaceId: string, dashboardId: string, filters?: AnalyticsFilters) => {
        set({ isLoading: true, error: null });
        try {
          const data = await analyticsApi.getDashboardData(workspaceId, dashboardId, filters);
          set({ dashboardData: data, isLoading: false });
        } catch (error: any) {
          set({ error: error.message, isLoading: false });
        }
      },

      createDashboard: async (workspaceId: string, data: any) => {
        set({ isLoading: true, error: null });
        try {
          const dashboard = await analyticsApi.createDashboard(workspaceId, data);
          const { dashboards } = get();
          set({ 
            dashboards: [...dashboards, dashboard],
            currentDashboard: dashboard,
            isLoading: false 
          });
        } catch (error: any) {
          set({ error: error.message, isLoading: false });
        }
      },

      updateDashboard: async (workspaceId: string, dashboardId: string, updates: any) => {
        set({ isLoading: true, error: null });
        try {
          const updatedDashboard = await analyticsApi.updateDashboard(workspaceId, dashboardId, updates);
          const { dashboards } = get();
          set({
            dashboards: dashboards.map(d => d.id === dashboardId ? updatedDashboard : d),
            currentDashboard: updatedDashboard,
            isLoading: false
          });
        } catch (error: any) {
          set({ error: error.message, isLoading: false });
        }
      },

      deleteDashboard: async (workspaceId: string, dashboardId: string) => {
        set({ isLoading: true, error: null });
        try {
          await analyticsApi.deleteDashboard(workspaceId, dashboardId);
          const { dashboards, currentDashboard } = get();
          const newDashboards = dashboards.filter(d => d.id !== dashboardId);
          set({
            dashboards: newDashboards,
            currentDashboard: currentDashboard?.id === dashboardId ? null : currentDashboard,
            isLoading: false
          });
        } catch (error: any) {
          set({ error: error.message, isLoading: false });
        }
      },

      // Report actions
      fetchReports: async (workspaceId: string) => {
        set({ isLoading: true, error: null });
        try {
          const reports = await analyticsApi.listReports(workspaceId);
          set({ reports, isLoading: false });
        } catch (error: any) {
          set({ error: error.message, isLoading: false });
        }
      },

      fetchReportTemplates: async (workspaceId: string) => {
        set({ isLoading: true, error: null });
        try {
          const templates = await analyticsApi.getReportTemplates(workspaceId);
          set({ reportTemplates: templates, isLoading: false });
        } catch (error: any) {
          set({ error: error.message, isLoading: false });
        }
      },

      createReport: async (workspaceId: string, data: any) => {
        set({ isLoading: true, error: null });
        try {
          const report = await analyticsApi.createReport(workspaceId, data);
          const { reports } = get();
          set({ reports: [...reports, report], isLoading: false });
        } catch (error: any) {
          set({ error: error.message, isLoading: false });
        }
      },

      executeReport: async (workspaceId: string, reportId: string, format?: string, filters?: any) => {
        set({ isLoading: true, error: null });
        try {
          const execution = await analyticsApi.executeReport(workspaceId, reportId, format, filters);
          // Store execution result
          const { reportExecutions } = get();
          const executions = reportExecutions[reportId] || [];
          set({
            reportExecutions: {
              ...reportExecutions,
              [reportId]: [execution, ...executions]
            },
            isLoading: false
          });
        } catch (error: any) {
          set({ error: error.message, isLoading: false });
        }
      },

      fetchReportHistory: async (workspaceId: string, reportId: string) => {
        set({ isLoading: true, error: null });
        try {
          const history = await analyticsApi.getReportHistory(workspaceId, reportId);
          const { reportExecutions } = get();
          set({
            reportExecutions: {
              ...reportExecutions,
              [reportId]: history.executions
            },
            isLoading: false
          });
        } catch (error: any) {
          set({ error: error.message, isLoading: false });
        }
      },

      scheduleReport: async (workspaceId: string, reportId: string, schedule: any) => {
        set({ isLoading: true, error: null });
        try {
          await analyticsApi.scheduleReport(workspaceId, reportId, schedule);
          set({ isLoading: false });
        } catch (error: any) {
          set({ error: error.message, isLoading: false });
        }
      },

      // KPI actions
      fetchKPIs: async (workspaceId: string, includeValues: boolean = true) => {
        set({ isLoading: true, error: null });
        try {
          const kpis = await analyticsApi.getKPIs(workspaceId, includeValues);
          set({ kpis, isLoading: false });
        } catch (error: any) {
          set({ error: error.message, isLoading: false });
        }
      },

      createKPI: async (workspaceId: string, data: any) => {
        set({ isLoading: true, error: null });
        try {
          const kpi = await analyticsApi.createKPI(workspaceId, data);
          const { kpis } = get();
          set({ kpis: [...kpis, kpi], isLoading: false });
        } catch (error: any) {
          set({ error: error.message, isLoading: false });
        }
      },

      updateKPI: async (workspaceId: string, kpiId: string, updates: any) => {
        set({ isLoading: true, error: null });
        try {
          const updatedKPI = await analyticsApi.updateKPI(workspaceId, kpiId, updates);
          const { kpis } = get();
          set({
            kpis: kpis.map(k => k.id === kpiId ? updatedKPI : k),
            isLoading: false
          });
        } catch (error: any) {
          set({ error: error.message, isLoading: false });
        }
      },

      deleteKPI: async (workspaceId: string, kpiId: string) => {
        set({ isLoading: true, error: null });
        try {
          await analyticsApi.deleteKPI(workspaceId, kpiId);
          const { kpis } = get();
          set({
            kpis: kpis.filter(k => k.id !== kpiId),
            isLoading: false
          });
        } catch (error: any) {
          set({ error: error.message, isLoading: false });
        }
      },

      // Metrics actions
      fetchOverviewMetrics: async (workspaceId: string, dateRange?: any) => {
        set({ isLoading: true, error: null });
        try {
          const metrics = await analyticsApi.getOverviewMetrics(workspaceId, dateRange);
          set({ overviewMetrics: metrics, isLoading: false });
        } catch (error: any) {
          set({ error: error.message, isLoading: false });
        }
      },

      fetchPipelineAnalytics: async (workspaceId: string, dateRange?: any) => {
        set({ isLoading: true, error: null });
        try {
          const analytics = await analyticsApi.getPipelineAnalytics(workspaceId, dateRange);
          set({ pipelineAnalytics: analytics, isLoading: false });
        } catch (error: any) {
          set({ error: error.message, isLoading: false });
        }
      },

      fetchGeographicDistribution: async (workspaceId: string, dateRange?: any) => {
        set({ isLoading: true, error: null });
        try {
          const distribution = await analyticsApi.getGeographicDistribution(workspaceId, dateRange);
          set({ geographicDistribution: distribution, isLoading: false });
        } catch (error: any) {
          set({ error: error.message, isLoading: false });
        }
      },

      fetchTimeSeries: async (
        workspaceId: string,
        metricType: string,
        metricName: string,
        dateRange: any,
        granularity: string = 'daily',
        dimensions?: any
      ) => {
        set({ isLoading: true, error: null });
        try {
          const data = await analyticsApi.getTimeSeries(
            workspaceId,
            metricType,
            metricName,
            dateRange,
            granularity,
            dimensions
          );
          const { timeSeriesData } = get();
          const key = `${metricType}_${metricName}`;
          set({
            timeSeriesData: {
              ...timeSeriesData,
              [key]: data
            },
            isLoading: false
          });
        } catch (error: any) {
          set({ error: error.message, isLoading: false });
        }
      },

      fetchComparison: async (
        workspaceId: string,
        metricType: string,
        metricName: string,
        currentPeriod: any,
        comparisonPeriod: any,
        dimensions?: any
      ) => {
        set({ isLoading: true, error: null });
        try {
          const comparison = await analyticsApi.getComparison(
            workspaceId,
            metricType,
            metricName,
            currentPeriod,
            comparisonPeriod,
            dimensions
          );
          // Store comparison data appropriately
          set({ isLoading: false });
        } catch (error: any) {
          set({ error: error.message, isLoading: false });
        }
      },

      // Activity actions
      recordActivity: async (workspaceId: string, activity: any) => {
        try {
          await analyticsApi.recordActivity(workspaceId, activity);
        } catch (error: any) {
          console.error('Failed to record activity:', error);
        }
      },

      recordMetric: async (workspaceId: string, metric: any) => {
        try {
          await analyticsApi.recordMetric(workspaceId, metric);
        } catch (error: any) {
          console.error('Failed to record metric:', error);
        }
      },

      // UI actions
      setFilters: (filters: AnalyticsFilters) => {
        set({ filters });
      },

      setCurrentDashboard: (dashboard: AnalyticsDashboard | null) => {
        set({ currentDashboard: dashboard });
      },

      clearError: () => {
        set({ error: null });
      }
    }),
    {
      name: 'analytics-store'
    }
  )
);
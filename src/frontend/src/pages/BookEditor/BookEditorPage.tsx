import { useRef, useState, useCallback, useEffect } from "react";
import ForwardedIconComponent from "@/components/common/genericIconComponent";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import AppHeader from "@/components/core/appHeaderComponent";
import useAlertStore from "@/stores/alertStore";
import useTheme from "@/customization/hooks/use-custom-theme";
import { useBookEditorStore } from "@/stores/bookEditor/bookEditorStore";

// Import book-specific components
import BookCanvas from "@/components/bookEditor/Canvas/BookCanvas";
import BookToolPalette from "@/components/bookEditor/ToolPalette/BookToolPalette";
import { BookMetadataPanel } from "./components/BookMetadataPanel";
import { PageNavigationBar } from "./components/PageNavigationBar";
import { BookProjectSelector } from "./components/BookProjectSelector";

// Import AI components
import { AIWritingAssistant } from "@/components/bookEditor/AI/AIWritingAssistant";
import { GenreSpecificTools } from "@/components/bookEditor/AI/GenreSpecificTools";
import { textAnalysisService } from "@/components/bookEditor/AI/TextAnalysisService";
import type { BookWritingContext, GenreSpecificConfig, BookGenre } from "@/components/bookEditor/AI/types";

interface PropertyPanelProps {
  selectedObjects: string[];
  onObjectUpdate: (objectId: string, properties: Record<string, any>) => void;
}

const PropertyPanel = ({ selectedObjects, onObjectUpdate }: PropertyPanelProps) => {
  // Simplified property panel for MVP
  return (
    <div className="space-y-4">
      <div className="text-sm font-medium text-muted-foreground">Object Properties</div>
      {selectedObjects.length === 0 ? (
        <div className="text-sm text-muted-foreground">
          Select an object to edit its properties
        </div>
      ) : (
        <div className="space-y-3">
          <div>
            <Label className="text-xs">Selected: {selectedObjects.length} object(s)</Label>
          </div>
          {/* Add property controls here based on selected object types */}
        </div>
      )}
    </div>
  );
};

export default function BookEditorPage() {
  useTheme(); // Apply custom theme
  
  const setSuccessData = useAlertStore((state) => state.setSuccessData);
  const setErrorData = useAlertStore((state) => state.setErrorData);
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  // Book Editor Store integration
  const {
    canvasState,
    bookMetadata,
    printSettings,
    selectedObjects,
    toolPalette,
    activeCanvas,
    isCanvasReady,
    currentPage,
    
    // Actions
    initializeBook,
    updateBookMetadata,
    updatePrintSettings,
    navigateToPage,
    addPage,
    removePage,
    duplicatePage,
    reorderPage,
    exportBook,
    exportPage,
    validateBook,
    setActiveTool,
    undo,
    redo,
    autoSave,
  } = useBookEditorStore();
  
  const [isProcessing, setIsProcessing] = useState(false);
  const [isServiceReady, setIsServiceReady] = useState(false);
  const [selectedProject, setSelectedProject] = useState<string | null>(null);
  
  // AI Assistant State
  const [aiAssistantOpen, setAiAssistantOpen] = useState(false);
  const [genreToolsOpen, setGenreToolsOpen] = useState(false);
  const [selectedText, setSelectedText] = useState<string>('');
  const [selectionPosition, setSelectionPosition] = useState<{ start: number; end: number } | undefined>();
  const [genreConfig, setGenreConfig] = useState<GenreSpecificConfig>({
    genre: (bookMetadata.genre as BookGenre) || 'fiction',
    specificSettings: {
      characterDevelopment: true,
      dialogueImprovement: true,
      plotStructure: true,
      sceneBuilding: true,
      continuityCheck: true,
    },
  });
  
  // Initialize book editor service
  useEffect(() => {
    // Initialize with default book settings
    initializeBook(
      {
        title: 'New Book Project',
        author: '',
        language: 'en',
      },
      {
        bookSize: 'TRADE_LARGE',
        dpi: 300,
        colorMode: 'CMYK',
      }
    );
    
    setIsServiceReady(true);
    setSuccessData({ title: "Book editor ready" });
  }, [initializeBook, setSuccessData]);

  // Auto-save functionality
  useEffect(() => {
    const interval = setInterval(() => {
      if (isServiceReady && canvasState.pages.length > 0) {
        autoSave().catch(console.error);
      }
    }, 30000); // Auto-save every 30 seconds

    return () => clearInterval(interval);
  }, [isServiceReady, canvasState.pages.length, autoSave]);

  const handleProjectUpload = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const projectData = e.target?.result as string;
          // TODO: Implement project loading
          setSelectedProject(file.name);
          setSuccessData({
            title: `Successfully loaded ${file.name}`,
          });
        } catch (error) {
          setErrorData({
            title: "Failed to load project",
            list: ["Invalid project file format"],
          });
        }
      };
      reader.readAsText(file);
    }
  }, [setSuccessData, setErrorData]);

  const handleToolSelect = useCallback((tool: any) => {
    // Handle AI tools specially
    if (tool.id === 'ai_writing_assistant') {
      setAiAssistantOpen(true);
      return;
    }
    if (tool.id === 'ai_genre_tools') {
      setGenreToolsOpen(true);
      return;
    }
    if (tool.id.startsWith('ai_')) {
      // Handle other AI tools by opening the assistant with specific operation
      const operationMap = {
        'ai_text_improvement': 'improve-text',
        'ai_grammar_check': 'check-grammar',
        'ai_research_assistant': 'research-assistance',
        'ai_dialogue_enhancement': 'improve-dialogue'
      };
      const operation = operationMap[tool.id] || 'generate-content';
      // TODO: Set operation in AI assistant
      setAiAssistantOpen(true);
      return;
    }
    
    setActiveTool(tool.id, tool.parameters || {});
  }, [setActiveTool]);

  const handlePageNavigation = useCallback((pageIndex: number) => {
    navigateToPage(pageIndex);
  }, [navigateToPage]);

  const handleAddPage = useCallback(() => {
    const newPage = addPage('content');
    navigateToPage(canvasState.pages.length - 1); // Navigate to new page
    setSuccessData({ title: "New page added" });
  }, [addPage, navigateToPage, canvasState.pages.length, setSuccessData]);

  const handleRemovePage = useCallback((pageIndex: number) => {
    if (canvasState.pages.length <= 1) {
      setErrorData({
        title: "Cannot remove page",
        list: ["Book must have at least one page"],
      });
      return;
    }
    
    removePage(pageIndex);
    setSuccessData({ title: "Page removed" });
  }, [removePage, canvasState.pages.length, setSuccessData, setErrorData]);

  const handleExportBook = async () => {
    if (!isServiceReady) return;
    
    setIsProcessing(true);
    
    try {
      const result = await exportBook({
        format: 'pdf',
        quality: 0.9,
        dpi: printSettings.dpi,
      });
      
      if (result.success) {
        setSuccessData({
          title: "Book exported successfully",
        });
      } else {
        throw new Error("Export failed");
      }
    } catch (error: any) {
      setErrorData({
        title: "Export failed",
        list: [error.message || "Please try again"],
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const handleValidateBook = async () => {
    if (!isServiceReady) return;
    
    setIsProcessing(true);
    
    try {
      const result = await validateBook();
      
      if (result.isCompliant) {
        setSuccessData({
          title: "Book is KDP compliant",
        });
      } else {
        setErrorData({
          title: "KDP compliance issues found",
          list: result.errors.map(error => error.message),
        });
      }
    } catch (error: any) {
      setErrorData({
        title: "Validation failed",
        list: [error.message || "Please try again"],
      });
    } finally {
      setIsProcessing(false);
    }
  };

  // AI Assistant handlers
  const handleAIContentUpdate = useCallback((content: string, operation: string) => {
    // Update the current page content with AI-generated content
    const currentPageObj = canvasState.pages[canvasState.currentPageIndex];
    if (currentPageObj && activeCanvas) {
      // Add text to canvas or replace selected text
      // TODO: Implement text insertion/replacement logic
      console.log('AI content update:', { content, operation });
      setSuccessData({ title: `${operation} completed successfully` });
    }
  }, [canvasState, activeCanvas, setSuccessData]);

  const handleAISuggestion = useCallback((suggestion: any) => {
    // Handle AI writing suggestions
    console.log('AI suggestion:', suggestion);
    // TODO: Show suggestion UI or apply automatically
  }, []);

  const handleTemplateGenerated = useCallback((template: any) => {
    // Handle AI-generated templates
    console.log('AI template generated:', template);
    // TODO: Apply template to current page or create new page
    setSuccessData({ title: 'Template generated successfully' });
  }, [setSuccessData]);

  const handleGenreToolAction = useCallback(async (action: string, parameters: any) => {
    // Handle genre-specific tool actions
    console.log('Genre tool action:', { action, parameters });
    try {
      // TODO: Implement specific genre tool actions
      setSuccessData({ title: `${action} completed` });
    } catch (error) {
      setErrorData({
        title: 'Genre tool error',
        list: [error instanceof Error ? error.message : 'Operation failed'],
      });
    }
  }, [setSuccessData, setErrorData]);

  // Create book writing context for AI
  const bookWritingContext: BookWritingContext = {
    bookMetadata: {
      title: bookMetadata.title,
      author: bookMetadata.author,
      genre: genreConfig.genre,
      targetAudience: 'General', // TODO: Add to book metadata
      language: bookMetadata.language,
    },
    currentPage: {
      pageNumber: canvasState.currentPageIndex + 1,
      pageType: canvasState.pages[canvasState.currentPageIndex]?.pageType || 'content',
      content: selectedText || '',
      wordCount: selectedText ? selectedText.split(/\s+/).length : 0,
    },
    structure: {
      totalPages: canvasState.pages.length,
      chapters: [], // TODO: Extract from book structure
      outline: '', // TODO: Generate from book content
      previousContent: '',
      nextPlannedContent: '',
    },
    style: {
      tone: 'neutral',
      pointOfView: 'third',
      tense: 'past',
      readingLevel: 12,
    },
  };

  return (
    <div className="flex flex-col h-screen bg-background overflow-hidden">
      {/* Main App Header */}
      <AppHeader />
      
      {/* Book Editor Specific Header */}
      <div className="border-b px-4 py-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <h1 className="text-lg font-semibold flex items-center gap-2">
              <ForwardedIconComponent name="BookOpen" className="h-5 w-5" />
              Book Editor Pro
            </h1>
            <span className="text-xs text-muted-foreground">Powered by Langflow</span>
          </div>
          
          <div className="flex items-center gap-1">
            {/* History controls */}
            <Button
              variant="ghost"
              size="sm"
              onClick={undo}
              disabled={!canvasState.canUndo}
            >
              <ForwardedIconComponent name="Undo2" className="h-3 w-3" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={redo}
              disabled={!canvasState.canRedo}
            >
              <ForwardedIconComponent name="Redo2" className="h-3 w-3" />
            </Button>
            
            <Separator orientation="vertical" className="h-4 mx-1" />
            
            {/* AI Assistant */}
            <Button
              variant="outline"
              size="sm"
              onClick={() => setAiAssistantOpen(true)}
              className="gap-1 text-xs"
            >
              <ForwardedIconComponent name="Sparkles" className="h-3 w-3" />
              AI Assistant
            </Button>
            
            <Separator orientation="vertical" className="h-4 mx-1" />
            
            {/* File operations */}
            <Button
              variant="outline"
              size="sm"
              onClick={() => fileInputRef.current?.click()}
              className="gap-1 text-xs"
            >
              <ForwardedIconComponent name="Upload" className="h-3 w-3" />
              Open Project
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              onClick={handleValidateBook}
              disabled={isProcessing}
              className="gap-1 text-xs"
            >
              <ForwardedIconComponent name="CheckCircle" className="h-3 w-3" />
              Validate KDP
            </Button>
            
            <Button
              variant="default"
              size="sm"
              onClick={handleExportBook}
              disabled={!isServiceReady || isProcessing}
              className="gap-1 text-xs"
            >
              <ForwardedIconComponent name="Download" className="h-3 w-3" />
              Export PDF
            </Button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex flex-1 overflow-hidden">
        {/* Left Sidebar - Tools and Project (Narrower) */}
        <div className="w-72 border-r bg-muted/30 flex flex-col overflow-hidden">
          {/* Project Selector */}
          <div className="p-3 border-b">
            <BookProjectSelector
              selectedProject={selectedProject}
              onProjectSelect={setSelectedProject}
              onNewProject={() => {
                initializeBook({
                  title: 'New Book Project',
                  author: '',
                });
                setSelectedProject(null);
              }}
            />
          </div>
          
          {/* Tools */}
          <div className="flex-1 p-3 overflow-y-auto">
            <h2 className="text-xs font-semibold mb-3 text-muted-foreground uppercase tracking-wider">Tools</h2>
            <BookToolPalette
              onToolSelect={handleToolSelect}
              selectedToolId={toolPalette.activeTool}
              disabled={!isServiceReady}
              layout="vertical"
              showSearch={true}
              showCategories={true}
              currentDocument={{
                currentPage: canvasState.currentPageIndex + 1,
                pageCount: canvasState.pages.length,
              }}
            />
          </div>
        </div>

        {/* Canvas Area (Expanded) */}
        <div className="flex-1 flex flex-col relative bg-muted/10">
          {/* Page Navigation Bar */}
          <PageNavigationBar
            currentPage={canvasState.currentPageIndex}
            totalPages={canvasState.pages.length}
            pages={canvasState.pages}
            onPageSelect={handlePageNavigation}
            onAddPage={handleAddPage}
            onRemovePage={handleRemovePage}
            onDuplicatePage={(pageIndex) => duplicatePage(pageIndex)}
            onReorderPage={reorderPage}
            showThumbnails={true}
          />
          
          {/* Main Canvas */}
          <div className="flex-1">
            <BookCanvas
              nodeId="book-editor-main"
              bookData={undefined}
              canvasConfig={{
                bookSize: printSettings.bookSize,
                dpi: printSettings.dpi,
                margins: printSettings.margins,
                showGrid: canvasState.gridVisible,
                showMargins: canvasState.showMargins,
                showBleed: canvasState.showBleed,
              }}
              currentPage={canvasState.currentPageIndex}
              showGrid={canvasState.gridVisible}
              showMargins={canvasState.showMargins}
              showBleed={canvasState.showBleed}
              enableZoom={true}
              enablePan={true}
              enablePageNavigation={false} // Handled by PageNavigationBar
              showPageThumbnails={false} // Handled by PageNavigationBar
              onCanvasReady={(canvas, state) => {
                console.log('Book canvas ready:', { canvas, state });
              }}
              onPageChanged={(pageIndex) => {
                console.log('Page changed to:', pageIndex);
              }}
              onStateChange={(state) => {
                console.log('Canvas state changed:', state);
              }}
              onError={(error) => {
                setErrorData({
                  title: "Canvas error",
                  list: [error],
                });
              }}
              className="h-full"
            />
          </div>
        </div>

        {/* Right Sidebar - Properties and Metadata (Narrower) */}
        <div className="w-80 border-l bg-muted/30 p-3 overflow-y-auto">
          <Tabs defaultValue="properties" className="w-full">
            <TabsList className="grid w-full grid-cols-3 mb-3 h-8">
              <TabsTrigger value="properties" className="text-xs">Properties</TabsTrigger>
              <TabsTrigger value="metadata" className="text-xs">Book Info</TabsTrigger>
              <TabsTrigger value="settings" className="text-xs">Print</TabsTrigger>
            </TabsList>
            
            <TabsContent value="properties" className="space-y-3 mt-0">
              <PropertyPanel
                selectedObjects={selectedObjects}
                onObjectUpdate={(objectId, properties) => {
                  console.log('Update object:', objectId, properties);
                }}
              />
            </TabsContent>
            
            <TabsContent value="metadata" className="space-y-3 mt-0">
              <BookMetadataPanel
                metadata={bookMetadata}
                onMetadataChange={updateBookMetadata}
                canvasState={canvasState}
              />
            </TabsContent>
            
            <TabsContent value="settings" className="space-y-4 mt-0">
              <div className="space-y-3">
                <div>
                  <Label className="text-xs mb-2 block">Book Size</Label>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="outline" size="sm" className="w-full justify-between text-xs">
                        {printSettings.bookSize}
                        <ForwardedIconComponent name="ChevronDown" className="h-3 w-3" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent>
                      <DropdownMenuItem onClick={() => updatePrintSettings({ bookSize: 'TRADE_LARGE' })}>
                        Trade Large (6" × 9")
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => updatePrintSettings({ bookSize: 'TRADE' })}>
                        Trade (5.5" × 8.5")
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => updatePrintSettings({ bookSize: 'DIGEST' })}>
                        Digest (5.5" × 8.25")
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>

                <div>
                  <Label className="text-xs mb-2 block">Print Quality</Label>
                  <div className="text-xs text-muted-foreground">
                    {printSettings.dpi} DPI • {printSettings.colorMode}
                  </div>
                </div>

                <div>
                  <Label className="text-xs mb-2 block">Paper Type</Label>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="outline" size="sm" className="w-full justify-between text-xs">
                        {printSettings.paperType}
                        <ForwardedIconComponent name="ChevronDown" className="h-3 w-3" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent>
                      <DropdownMenuItem onClick={() => updatePrintSettings({ paperType: 'white' })}>
                        White Paper
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => updatePrintSettings({ paperType: 'cream' })}>
                        Cream Paper
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>

                <Button 
                  className="w-full text-xs" 
                  onClick={handleExportBook} 
                  disabled={!isServiceReady || isProcessing}
                  size="sm"
                >
                  <ForwardedIconComponent name="Download" className="h-3 w-3 mr-1" />
                  {isProcessing ? "Exporting..." : "Export for KDP"}
                </Button>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </div>

      {/* AI Writing Assistant Modal */}
      <AIWritingAssistant
        isOpen={aiAssistantOpen}
        onClose={() => setAiAssistantOpen(false)}
        bookContext={bookWritingContext}
        selectedText={selectedText}
        selectionPosition={selectionPosition}
        onContentUpdate={handleAIContentUpdate}
        onSuggestion={handleAISuggestion}
        onTemplateGenerated={handleTemplateGenerated}
        config={{
          enableRealTime: true,
          showAdvancedOptions: false,
          compactMode: false,
        }}
      />

      {/* Genre-Specific Tools Modal */}
      {genreToolsOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[80vh] overflow-hidden">
            <div className="flex items-center justify-between p-4 border-b">
              <h2 className="text-lg font-semibold">Genre-Specific Writing Tools</h2>
              <Button variant="ghost" size="sm" onClick={() => setGenreToolsOpen(false)}>
                <ForwardedIconComponent name="X" className="h-4 w-4" />
              </Button>
            </div>
            <div className="p-4 overflow-y-auto">
              <GenreSpecificTools
                genre={genreConfig.genre}
                genreConfig={genreConfig}
                bookContext={bookWritingContext}
                onConfigUpdate={setGenreConfig}
                onToolAction={handleGenreToolAction}
              />
            </div>
          </div>
        </div>
      )}

      <input
        ref={fileInputRef}
        type="file"
        accept=".json,.book"
        onChange={handleProjectUpload}
        className="hidden"
      />
    </div>
  );
}
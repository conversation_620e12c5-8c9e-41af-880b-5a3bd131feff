import React, { useEffect, useState } from 'react';
import { AnalyticsDashboard } from '@/components/analytics';
import { useAnalyticsStore } from '@/stores/analyticsStore';
import { Loader2, AlertCircle } from 'lucide-react';
import { useWorkspaceContext } from '@/hooks/useWorkspaceContext';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';

const AnalyticsPage: React.FC = () => {
  const {
    dashboards,
    currentDashboard,
    isLoading,
    error,
    fetchDashboards,
    setCurrentDashboard,
    clearError
  } = useAnalyticsStore();

  const { currentWorkspace } = useWorkspaceContext();
  const workspaceId = currentWorkspace?.id;

  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    const initializeAnalytics = async () => {
      try {
        if (workspaceId) {
          await fetchDashboards(workspaceId);
        }
        setIsInitialized(true);
      } catch (err) {
        console.error('Failed to initialize analytics:', err);
      }
    };

    if (!isInitialized && workspaceId) {
      initializeAnalytics();
    }
  }, [fetchDashboards, isInitialized, workspaceId]);

  // Set the first dashboard as current if none is selected
  useEffect(() => {
    if (!currentDashboard && dashboards && Array.isArray(dashboards) && dashboards.length > 0 && isInitialized) {
      const defaultDashboard = dashboards.find(d => d.is_default) || dashboards[0];
      setCurrentDashboard(defaultDashboard);
    }
  }, [dashboards, currentDashboard, setCurrentDashboard, isInitialized]);

  if (isLoading && !isInitialized) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-muted-foreground">Loading analytics...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto p-6">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>
            {error}
            <Button
              variant="outline"
              size="sm"
              className="ml-4"
              onClick={() => {
                clearError();
                if (workspaceId) {
                  fetchDashboards(workspaceId);
                }
              }}
            >
              Retry
            </Button>
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  if (!currentDashboard) {
    return (
      <div className="container mx-auto p-6">
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>No dashboards available</AlertTitle>
          <AlertDescription>
            Create your first analytics dashboard to start tracking metrics.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  if (!workspaceId) {
    return (
      <div className="container mx-auto p-6">
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>No workspace selected</AlertTitle>
          <AlertDescription>
            Please select a workspace to view analytics.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="h-full w-full">
      <AnalyticsDashboard workspaceId={workspaceId} />
    </div>
  );
};

export default AnalyticsPage;
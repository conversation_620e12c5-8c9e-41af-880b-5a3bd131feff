/**
 * IDEDemo - Complete IDE demonstration page
 * Leverages new component architecture per LEVER framework
 */

import { AlertTriangle } from "lucide-react";
import { useEffect } from "react";
import { ErrorBoundary } from "react-error-boundary";
import { useParams, useSearchParams } from "react-router-dom";
import { IDEWorkspace } from "@/components/ide/IDEWorkspace";
import { Button } from "@/components/ui/button";
import AppHeader from "@/components/core/appHeaderComponent";
import { MenuBar } from "@/components/navigation/MenuBar";
import useTheme from "@/customization/hooks/use-custom-theme";

interface ErrorFallbackProps {
  error: Error;
  resetErrorBoundary: () => void;
}

function ErrorFallback({ error, resetErrorBoundary }: ErrorFallbackProps) {
  return (
    <div className="flex h-screen items-center justify-center">
      <div className="text-center space-y-4">
        <AlertTriangle className="h-12 w-12 text-red-500 mx-auto" />
        <h2 className="text-lg font-semibold">IDE Error</h2>
        <p className="text-sm text-muted-foreground">
          Something went wrong with the IDE interface
        </p>
        <pre className="text-xs bg-muted p-4 rounded max-w-md overflow-auto">
          {error.message}
        </pre>
        <Button onClick={resetErrorBoundary}>Try Again</Button>
      </div>
    </div>
  );
}

export default function IDEDemo() {
  useTheme(); // Apply custom theme
  
  const { projectPath } = useParams<{ projectPath?: string }>();
  const [searchParams] = useSearchParams();

  // Get project path from URL params or search params
  const initialProjectPath =
    projectPath ||
    searchParams.get("project") ||
    "/Users/<USER>/Projects/own/turbois";

  useEffect(() => {
    // Set document title
    document.title = "TurboIs IDE - Development Environment";

    // Prevent default browser shortcuts that conflict with IDE
    const handleKeyDown = (e: KeyboardEvent) => {
      // Prevent browser refresh on Ctrl+R
      if ((e.ctrlKey || e.metaKey) && e.key === "r") {
        e.preventDefault();
      }

      // Prevent browser find on Ctrl+F (let IDE handle it)
      if ((e.ctrlKey || e.metaKey) && e.key === "f") {
        e.preventDefault();
      }

      // Prevent browser new tab on Ctrl+T
      if ((e.ctrlKey || e.metaKey) && e.key === "t") {
        e.preventDefault();
      }
    };

    document.addEventListener("keydown", handleKeyDown);

    return () => {
      document.removeEventListener("keydown", handleKeyDown);
      document.title = "TurboIs"; // Reset title
    };
  }, []);

  return (
    <ErrorBoundary
      FallbackComponent={ErrorFallback}
      onReset={() => window.location.reload()}
    >
      <div className="h-screen w-full overflow-hidden flex flex-col">
        {/* Main navbar consistent with other modules */}
        <AppHeader />
        <MenuBar />
        
        {/* IDE workspace takes remaining height */}
        <div className="flex-1 overflow-hidden">
          <IDEWorkspace projectPath={initialProjectPath} className="h-full" />
        </div>
      </div>
    </ErrorBoundary>
  );
}

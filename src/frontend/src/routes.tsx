import { lazy, Suspense } from "react";
import {
  createBrowserRouter,
  createRoutesFromElements,
  Outlet,
  Route,
} from "react-router-dom";
import { ProtectedAdminRoute } from "./components/authorization/authAdminGuard";
import { ProtectedRoute } from "./components/authorization/authGuard";
import { ProtectedLoginRoute } from "./components/authorization/authLoginGuard";
import { AuthSettingsGuard } from "./components/authorization/authSettingsGuard";
import ChunkErrorBoundary from "./components/common/ChunkErrorBoundary";
import { 
  PhotoEditorLoading, 
  IDELoading, 
  TaskManagementLoading, 
  ChatLoading, 
  GeneralLoading 
} from "./components/common/LoadingStates";
import ContextWrapper from "./contexts";
import CustomDashboardWrapperPage from "./customization/components/custom-DashboardWrapperPage";
import { CustomNavigate } from "./customization/components/custom-navigate";
import { BASENAME } from "./customization/config-constants";
import {
  ENABLE_CUSTOM_PARAM,
  ENABLE_FILE_MANAGEMENT,
} from "./customization/feature-flags";
import { CustomRoutesStore } from "./customization/utils/custom-routes-store";
import { CustomRoutesStorePages } from "./customization/utils/custom-routes-store-pages";
import { AppAuthenticatedPage } from "./pages/AppAuthenticatedPage";
import { AppInitPage } from "./pages/AppInitPage";
import { AppWrapperPage } from "./pages/AppWrapperPage";
import FlowPage from "./pages/FlowPage";
import LoginPage from "./pages/LoginPage";
import FilesPage from "./pages/MainPage/pages/filesPage";
import HomePage from "./pages/MainPage/pages/homePage";
import CollectionPage from "./pages/MainPage/pages/main-page";
import SettingsPage from "./pages/SettingsPage";
import GeneralPage from "./pages/SettingsPage/pages/GeneralPage";
import ViewPage from "./pages/ViewPage";

// Heavy feature pages - lazy loaded with error boundaries
const AdminPage = lazy(() => import("./pages/AdminPage"));
const ChatPage = lazy(() => import("./pages/ChatPage"));
const LoginAdminPage = lazy(() => import("./pages/AdminPage/LoginPage"));
const DeleteAccountPage = lazy(() => import("./pages/DeleteAccountPage"));
const PhotoEditorPage = lazy(() => import("./pages/PhotoEditorPage"));
const BookEditorPage = lazy(() => import("./pages/BookEditor/BookEditorPage"));
const VideoEditorPage = lazy(() => import("./pages/VideoEditorPage"));
const IDEDemo = lazy(() => import("./pages/IDEDemo"));
const PlaygroundPage = lazy(() => import("./pages/Playground"));
const SignUp = lazy(() => import("./pages/SignUpPage"));

// Task management components - lazy loaded
const TaskBoardList = lazy(() => import("./components/task-management/TaskBoardList"));
const TaskKanbanBoard = lazy(() => import("./components/task-management/TaskKanbanBoard"));

// CRM components - lazy loaded
const CRMPage = lazy(() => import("./pages/CRMPage"));

// Analytics components - lazy loaded
const AnalyticsPage = lazy(() => import("./pages/AnalyticsPage"));

// Error Dashboard - lazy loaded
const ErrorDashboardPage = lazy(() => import("./pages/ErrorDashboardPage"));

// Settings pages - lazy loaded for smaller initial bundle
const ApiKeysPage = lazy(() => import("./pages/SettingsPage/pages/ApiKeysPage"));
const AgentsPage = lazy(() => import("./pages/SettingsPage/pages/agentsPage"));
const GlobalVariablesPage = lazy(() => import("./pages/SettingsPage/pages/GlobalVariablesPage"));
const MCPServersPage = lazy(() => import("./pages/SettingsPage/pages/MCPServersPage"));
const WorkspaceConfigPage = lazy(() => import("./pages/SettingsPage/pages/workspaceConfigPage"));
const MessagesPage = lazy(() => import("./pages/SettingsPage/pages/messagesPage"));
const ShortcutsPage = lazy(() => import("./pages/SettingsPage/pages/ShortcutsPage"));
// const ErrorDashboard = lazy(() => import("./components/monitoring/ErrorDashboard").then(module => ({ default: module.ErrorDashboard })));

// Higher-order component for wrapping lazy components with error boundaries
const withLazyErrorBoundary = (LazyComponent: React.LazyExoticComponent<any>, LoadingComponent: React.ComponentType) => {
  return (props: any) => (
    <ChunkErrorBoundary>
      <Suspense fallback={<LoadingComponent />}>
        <LazyComponent {...props} />
      </Suspense>
    </ChunkErrorBoundary>
  );
};

// Create error-boundary wrapped components
const SafePhotoEditorPage = withLazyErrorBoundary(PhotoEditorPage, PhotoEditorLoading);
const SafeBookEditorPage = withLazyErrorBoundary(BookEditorPage, GeneralLoading);
const SafeVideoEditorPage = withLazyErrorBoundary(VideoEditorPage, PhotoEditorLoading);
const SafeIDEDemo = withLazyErrorBoundary(IDEDemo, IDELoading);
const SafeChatPage = withLazyErrorBoundary(ChatPage, ChatLoading);
const SafeTaskBoardList = withLazyErrorBoundary(TaskBoardList, TaskManagementLoading);
const SafeTaskKanbanBoard = withLazyErrorBoundary(TaskKanbanBoard, TaskManagementLoading);
const SafeCRMPage = withLazyErrorBoundary(CRMPage, GeneralLoading);
const SafeAnalyticsPage = withLazyErrorBoundary(AnalyticsPage, GeneralLoading);
const SafeErrorDashboardPage = withLazyErrorBoundary(ErrorDashboardPage, GeneralLoading);

const router = createBrowserRouter(
  createRoutesFromElements([
    <Route path="/playground/:id/">
      <Route
        path=""
        element={
          <ContextWrapper key={1}>
            <ChunkErrorBoundary>
              <Suspense fallback={<GeneralLoading />}>
                <PlaygroundPage />
              </Suspense>
            </ChunkErrorBoundary>
          </ContextWrapper>
        }
      />
    </Route>,
    <Route
      path={ENABLE_CUSTOM_PARAM ? "/:customParam?" : "/"}
      element={
        <ContextWrapper key={2}>
          <Outlet />
        </ContextWrapper>
      }
    >
      <Route path="" element={<AppInitPage />}>
        <Route path="" element={<AppWrapperPage />}>
          <Route
            path=""
            element={
              <ProtectedRoute>
                <Outlet />
              </ProtectedRoute>
            }
          >
            <Route path="" element={<AppAuthenticatedPage />}>
              <Route path="photo-editor" element={<SafePhotoEditorPage />} />
              <Route path="book-editor" element={<SafeBookEditorPage />} />
              <Route path="video-editor" element={<SafeVideoEditorPage />} />
              <Route path="ide-demo" element={<SafeIDEDemo />} />
              <Route path="ide" element={<SafeIDEDemo />} />
              <Route path="" element={<CustomDashboardWrapperPage />}>
                <Route path="" element={<CollectionPage />}>
                  <Route
                    index
                    element={<CustomNavigate replace to={"flows"} />}
                  />
                  {ENABLE_FILE_MANAGEMENT && (
                    <Route path="files" element={<FilesPage />} />
                  )}
                  <Route
                    path="flows/"
                    element={<HomePage key="flows" type="flows" />}
                  />
                  <Route
                    path="components/"
                    element={<HomePage key="components" type="components" />}
                  >
                    <Route
                      path="folder/:folderId"
                      element={<HomePage key="components" type="components" />}
                    />
                  </Route>
                  <Route
                    path="all/"
                    element={<HomePage key="flows" type="flows" />}
                  >
                    <Route
                      path="folder/:folderId"
                      element={<HomePage key="flows" type="flows" />}
                    />
                  </Route>
                  <Route
                    path="mcp/"
                    element={<HomePage key="mcp" type="mcp" />}
                  >
                    <Route
                      path="folder/:folderId"
                      element={<HomePage key="mcp" type="mcp" />}
                    />
                  </Route>
                </Route>
                <Route path="chat" element={<SafeChatPage />} />
                <Route path="crm" element={<SafeCRMPage />} />
                <Route path="analytics" element={<SafeAnalyticsPage />} />
                <Route path="task-management">
                  <Route index element={<SafeTaskBoardList />} />
                  <Route path="boards/:boardId" element={<SafeTaskKanbanBoard />} />
                </Route>
                <Route path="settings" element={<SettingsPage />}>
                  <Route
                    index
                    element={<CustomNavigate replace to={"general"} />}
                  />
                  <Route path="workspace-config" element={
                    <ChunkErrorBoundary>
                      <Suspense fallback={<GeneralLoading />}>
                        <WorkspaceConfigPage />
                      </Suspense>
                    </ChunkErrorBoundary>
                  } />
                  <Route path="agents" element={
                    <ChunkErrorBoundary>
                      <Suspense fallback={<GeneralLoading />}>
                        <AgentsPage />
                      </Suspense>
                    </ChunkErrorBoundary>
                  } />
                  <Route
                    path="global-variables"
                    element={
                      <ChunkErrorBoundary>
                        <Suspense fallback={<GeneralLoading />}>
                          <GlobalVariablesPage />
                        </Suspense>
                      </ChunkErrorBoundary>
                    }
                  />
                  <Route path="mcp-servers" element={
                    <ChunkErrorBoundary>
                      <Suspense fallback={<GeneralLoading />}>
                        <MCPServersPage />
                      </Suspense>
                    </ChunkErrorBoundary>
                  } />
                  <Route path="api-keys" element={
                    <ChunkErrorBoundary>
                      <Suspense fallback={<GeneralLoading />}>
                        <ApiKeysPage />
                      </Suspense>
                    </ChunkErrorBoundary>
                  } />
                  <Route
                    path="general/:scrollId?"
                    element={
                      <AuthSettingsGuard>
                        <GeneralPage />
                      </AuthSettingsGuard>
                    }
                  />
                  <Route path="shortcuts" element={
                    <ChunkErrorBoundary>
                      <Suspense fallback={<GeneralLoading />}>
                        <ShortcutsPage />
                      </Suspense>
                    </ChunkErrorBoundary>
                  } />
                  <Route path="error-monitoring" element={
                    <ChunkErrorBoundary>
                      <Suspense fallback={<GeneralLoading />}>
                        <SafeErrorDashboardPage />
                      </Suspense>
                    </ChunkErrorBoundary>
                  } />
                  <Route path="messages" element={
                    <ChunkErrorBoundary>
                      <Suspense fallback={<GeneralLoading />}>
                        <MessagesPage />
                      </Suspense>
                    </ChunkErrorBoundary>
                  } />
                  {CustomRoutesStore()}
                </Route>
                {CustomRoutesStorePages()}
                <Route path="account">
                  <Route path="delete" element={<DeleteAccountPage />}></Route>
                </Route>
                <Route
                  path="admin"
                  element={
                    <ProtectedAdminRoute>
                      <ChunkErrorBoundary>
                        <Suspense fallback={<GeneralLoading />}>
                          <AdminPage />
                        </Suspense>
                      </ChunkErrorBoundary>
                    </ProtectedAdminRoute>
                  }
                />
              </Route>
              <Route path="flow/:id/">
                <Route path="" element={<CustomDashboardWrapperPage />}>
                  <Route path="folder/:folderId/" element={<FlowPage />} />
                  <Route path="" element={<FlowPage />} />
                </Route>
                <Route path="view" element={<ViewPage />} />
              </Route>
            </Route>
          </Route>
          <Route
            path="login"
            element={
              <ProtectedLoginRoute>
                <LoginPage />
              </ProtectedLoginRoute>
            }
          />
          {/* Development routes - no authentication required */}
          {process.env.NODE_ENV === 'development' && (
            <Route path="error-dashboard" element={<SafeErrorDashboardPage />} />
          )}
          <Route
            path="signup"
            element={
              <ProtectedLoginRoute>
                <ChunkErrorBoundary>
                  <Suspense fallback={<GeneralLoading />}>
                    <SignUp />
                  </Suspense>
                </ChunkErrorBoundary>
              </ProtectedLoginRoute>
            }
          />
          <Route
            path="login/admin"
            element={
              <ProtectedLoginRoute>
                <ChunkErrorBoundary>
                  <Suspense fallback={<GeneralLoading />}>
                    <LoginAdminPage />
                  </Suspense>
                </ChunkErrorBoundary>
              </ProtectedLoginRoute>
            }
          />
        </Route>
      </Route>
      <Route path="*" element={<CustomNavigate replace to="/" />} />
    </Route>,
  ]),
  { basename: BASENAME || undefined },
);

export default router;

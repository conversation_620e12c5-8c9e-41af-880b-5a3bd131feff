import React, { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Progress } from "@/components/ui/progress";
import { useCRMConfiguration } from "@/hooks/useCRMConfiguration";
import { useCRMStore } from "@/stores/crmStore";
import { useGetCustomers } from "@/controllers/API/queries/crm";
import { BarChart, Users, TrendingUp, DollarSign, Clock, Target, Settings } from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import { cn } from "@/lib/utils";

export function ConfigurableCRMDashboard() {
  const { 
    salesPipeline, 
    customerFields,
    uiPreferences,
    getStageName,
    getStageColor,
    isFieldRequired,
    loading: configLoading 
  } = useCRMConfiguration();
  
  const { customers } = useCRMStore();
  const { data: customersData, isLoading: customersLoading } = useGetCustomers({ limit: 100 });
  
  const [selectedPeriod, setSelectedPeriod] = useState("7d");

  // Calculate metrics based on configuration
  const calculatePipelineMetrics = () => {
    if (!customersData || !Array.isArray(customersData) || !salesPipeline?.stages || !Array.isArray(salesPipeline.stages)) {
      return [];
    }

    return salesPipeline.stages.map(stage => {
      const count = customersData.filter(c => c.stage === stage.id).length;
      const percentage = customersData.length > 0 ? (count / customersData.length) * 100 : 0;
      
      return {
        stage,
        count,
        percentage,
        value: count * 10000, // Mock value calculation
      };
    });
  };

  const pipelineMetrics = calculatePipelineMetrics();
  const totalPipelineValue = pipelineMetrics.reduce((sum, m) => sum + m.value, 0);

  if (configLoading || customersLoading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading CRM configuration...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with Configuration Status */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">CRM Dashboard</h2>
          <p className="text-muted-foreground">
            Configured for your {salesPipeline?.stages?.length || 0}-stage sales pipeline
          </p>
        </div>
        <div className="flex items-center gap-4">
          <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="24h">Last 24h</SelectItem>
              <SelectItem value="7d">Last 7 days</SelectItem>
              <SelectItem value="30d">Last 30 days</SelectItem>
              <SelectItem value="90d">Last 90 days</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" size="sm">
            <Settings className="h-4 w-4 mr-2" />
            Configure
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Customers</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{Array.isArray(customersData) ? customersData.length : 0}</div>
            <p className="text-xs text-muted-foreground">
              +12% from last period
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pipeline Value</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              ${(totalPipelineValue / 1000).toFixed(0)}k
            </div>
            <p className="text-xs text-muted-foreground">
              Across all stages
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Conversion Rate</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">23.5%</div>
            <p className="text-xs text-muted-foreground">
              Lead to close ratio
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg. Deal Time</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">21 days</div>
            <p className="text-xs text-muted-foreground">
              From lead to close
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Sales Pipeline Overview */}
      <Card>
        <CardHeader>
          <CardTitle>Sales Pipeline Overview</CardTitle>
          <CardDescription>
            Customer distribution across your configured pipeline stages
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {pipelineMetrics.map((metric, index) => (
              <div key={metric.stage.id} className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <div 
                      className="w-3 h-3 rounded-full" 
                      style={{ backgroundColor: metric.stage.color }}
                    />
                    <span className="font-medium">{metric.stage.name}</span>
                    <Badge variant="secondary" className="text-xs">
                      {metric.count}
                    </Badge>
                  </div>
                  <span className="text-sm text-muted-foreground">
                    ${(metric.value / 1000).toFixed(0)}k
                  </span>
                </div>
                <Progress 
                  value={metric.percentage} 
                  className="h-2"
                  style={{
                    // Apply stage color to progress bar
                    '--progress-background': metric.stage.color,
                  } as React.CSSProperties}
                />
                {metric.stage.description && (
                  <p className="text-xs text-muted-foreground">
                    {metric.stage.description}
                  </p>
                )}
                {metric.stage.auto_move_days && (
                  <p className="text-xs text-muted-foreground">
                    Auto-advances after {metric.stage.auto_move_days} days
                  </p>
                )}
              </div>
            ))}
          </div>

          {/* Stage Transitions */}
          <div className="mt-6 pt-6 border-t">
            <h4 className="text-sm font-semibold mb-3">Stage Transitions</h4>
            <div className="flex items-center gap-2 flex-wrap">
              {salesPipeline?.stages?.map((stage, index) => {
                const transitions = salesPipeline?.stage_transitions?.[stage.id] || [];
                return (
                  <React.Fragment key={stage.id}>
                    <Badge 
                      variant="outline" 
                      style={{ borderColor: stage.color, color: stage.color }}
                    >
                      {stage.name}
                    </Badge>
                    {transitions.length > 0 && index < (salesPipeline?.stages?.length || 0) - 1 && (
                      <span className="text-muted-foreground">→</span>
                    )}
                  </React.Fragment>
                );
              })}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Configuration Summary */}
      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Customer Field Configuration</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center justify-between text-sm">
                <span className="text-muted-foreground">Required Fields</span>
                <Badge>{customerFields.required_fields.length}</Badge>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span className="text-muted-foreground">Custom Fields</span>
                <Badge>{customerFields.custom_fields.length}</Badge>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span className="text-muted-foreground">Searchable Fields</span>
                <Badge>
                  {customerFields.custom_fields.filter(f => f.searchable).length}
                </Badge>
              </div>
              <div className="pt-2 border-t">
                <p className="text-xs text-muted-foreground">
                  Required: {customerFields.required_fields.join(", ") || "None"}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-base">UI Preferences</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center justify-between text-sm">
                <span className="text-muted-foreground">Theme Mode</span>
                <Badge variant="outline">
                  {uiPreferences.theme.dark_mode ? "Dark" : "Light"}
                </Badge>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span className="text-muted-foreground">Default View</span>
                <Badge variant="outline">
                  {uiPreferences.layout.default_view}
                </Badge>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span className="text-muted-foreground">Items Per Page</span>
                <Badge variant="outline">
                  {uiPreferences.layout.items_per_page}
                </Badge>
              </div>
              <div className="pt-2 border-t">
                <p className="text-xs text-muted-foreground">
                  These preferences affect how CRM data is displayed
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
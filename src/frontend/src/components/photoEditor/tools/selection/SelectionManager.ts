import type { Canvas, FabricObject, TPointerEvent } from "fabric";
import * as fabric from "fabric";
import mitt from 'mitt';

type Events = {
  selectionCreated: SelectionData;
  selectionStarted: [SelectionType, SelectionOptions];
  selectionRefined: [string, number];
  selectionTransformed: any;
  selectionToLayer: string;
  selectionSaved: [string, string];
  selectionLoaded: string;
  selectionCleared: void;
  selectionError: Error;
  selectionCancelled: void;
  selectionDeleted: string;
  quickMaskCancelled: void;
};
import { LayerManager } from "../../layers/LayerManager";

/**
 * SelectionManager - Professional Selection Tools for TurboIS Photo Editor
 * 
 * Implements advanced selection capabilities including:
 * - Rectangle and ellipse selections
 * - Lasso and polygonal lasso tools
 * - Magic wand with flood-fill algorithm
 * - Quick mask mode for brush-based refinement
 * - Selection refinement operations
 * - Selection transformations and layer conversion
 * 
 * Performance targets: <100ms for basic selections, <500ms for complex operations
 * LEVER Compliance: Leverages Fabric.js, extends existing patterns, verified through testing
 */

export enum SelectionType {
  RECTANGLE = 'rectangle',
  ELLIPSE = 'ellipse',
  LASSO = 'lasso',
  POLYGONAL_LASSO = 'polygonal_lasso',
  MAGIC_WAND = 'magic_wand',
  QUICK_MASK = 'quick_mask'
}

export interface SelectionData {
  id: string;
  type: SelectionType;
  path: string; // SVG path data
  bounds: fabric.Rect;
  featherRadius: number;
  antiAlias: boolean;
  tolerance: number; // for magic wand
  created: Date;
  metadata: Record<string, any>;
}

export interface SelectionOptions {
  featherRadius?: number;
  antiAlias?: boolean;
  tolerance?: number;
  addToSelection?: boolean;
  subtractFromSelection?: boolean;
  intersectWithSelection?: boolean;
  smoothing?: number;
  contraction?: number;
  expansion?: number;
}

export interface FloodFillPixel {
  x: number;
  y: number;
}

/**
 * SelectionManager handles all advanced selection operations
 */
export class SelectionManager {
  private emitter = mitt<Events>();
  private canvas: Canvas;
  private layerManager: LayerManager;
  private currentSelection: SelectionData | null = null;
  private selectionHistory: SelectionData[] = [];
  private selectionPath: fabric.Path | null = null;
  private isSelecting: boolean = false;
  private selectionType: SelectionType = SelectionType.RECTANGLE;
  private previewPath: fabric.FabricObject | null = null;

  // Magic wand specific properties
  private imageDataCache: ImageData | null = null;
  private imageDataCacheTime: number = 0;
  private readonly CACHE_DURATION = 1000; // 1 second cache

  // Quick mask properties
  private maskOverlay: fabric.Rect | null = null;
  private maskCanvas: HTMLCanvasElement | null = null;
  private maskContext: CanvasRenderingContext2D | null = null;

  // Performance monitoring
  private performanceEnabled: boolean = process.env.NODE_ENV === 'development';

  constructor(canvas: Canvas, layerManager: LayerManager) {
    this.canvas = canvas;
    this.layerManager = layerManager;
    this.setupCanvasEvents();
    this.initializeMaskCanvas();
  }

  /**
   * Initialize mask canvas for quick mask mode
   */
  private initializeMaskCanvas(): void {
    this.maskCanvas = document.createElement('canvas');
    this.maskCanvas.width = this.canvas.getWidth();
    this.maskCanvas.height = this.canvas.getHeight();
    this.maskContext = this.maskCanvas.getContext('2d');
  }

  /**
   * Start selection process with specified tool
   */
  startSelection(type: SelectionType, options: SelectionOptions = {}): void {
    const startTime = this.performanceEnabled ? performance.now() : 0;

    this.selectionType = type;
    this.isSelecting = true;
    this.canvas.selection = false; // Disable default selection

    // Clear any existing preview
    this.clearPreview();

    switch (type) {
      case SelectionType.RECTANGLE:
        this.startRectangleSelection(options);
        break;
      case SelectionType.ELLIPSE:
        this.startEllipseSelection(options);
        break;
      case SelectionType.LASSO:
        this.startLassoSelection(options);
        break;
      case SelectionType.POLYGONAL_LASSO:
        this.startPolygonalLassoSelection(options);
        break;
      case SelectionType.MAGIC_WAND:
        this.startMagicWandSelection(options);
        break;
      case SelectionType.QUICK_MASK:
        this.startQuickMaskMode(options);
        break;
    }

    if (this.performanceEnabled) {
      const duration = performance.now() - startTime;
      console.log(`[SelectionManager] Started ${type} selection in ${duration.toFixed(2)}ms`);
    }

    this.emitter.emit('selectionStarted', type, options);
  }

  /**
   * Rectangle selection tool
   */
  private startRectangleSelection(options: SelectionOptions): void {
    let startPoint: fabric.Point;
    let rectangle: fabric.Rect;

    const onMouseDown = (e: TPointerEvent) => {
      if (!e.pointer) return;
      startPoint = e.pointer;
      
      rectangle = new fabric.Rect({
        left: startPoint.x,
        top: startPoint.y,
        width: 0,
        height: 0,
        fill: 'transparent',
        stroke: '#007bff',
        strokeWidth: 1,
        strokeDashArray: [5, 5],
        selectable: false,
        evented: false,
        excludeFromExport: true
      });

      this.previewPath = rectangle;
      this.canvas.add(rectangle);
    };

    const onMouseMove = (e: TPointerEvent) => {
      if (!e.pointer || !rectangle || !startPoint) return;

      const currentPoint = e.pointer;
      const width = Math.abs(currentPoint.x - startPoint.x);
      const height = Math.abs(currentPoint.y - startPoint.y);
      const left = Math.min(startPoint.x, currentPoint.x);
      const top = Math.min(startPoint.y, currentPoint.y);

      rectangle.set({
        left,
        top,
        width,
        height
      });

      this.canvas.requestRenderAll();
    };

    const onMouseUp = () => {
      if (rectangle) {
        this.finalizeRectangleSelection(rectangle, options);
        this.canvas.remove(rectangle);
      }
      this.cleanupSelectionEvents();
    };

    this.canvas.on('mouse:down', onMouseDown);
    this.canvas.on('mouse:move', onMouseMove);
    this.canvas.on('mouse:up', onMouseUp);
  }

  /**
   * Ellipse selection tool
   */
  private startEllipseSelection(options: SelectionOptions): void {
    let startPoint: fabric.Point;
    let ellipse: fabric.Ellipse;

    const onMouseDown = (e: TPointerEvent) => {
      if (!e.pointer) return;
      startPoint = e.pointer;
      
      ellipse = new fabric.Ellipse({
        left: startPoint.x,
        top: startPoint.y,
        rx: 0,
        ry: 0,
        fill: 'transparent',
        stroke: '#007bff',
        strokeWidth: 1,
        strokeDashArray: [5, 5],
        selectable: false,
        evented: false,
        excludeFromExport: true
      });

      this.previewPath = ellipse;
      this.canvas.add(ellipse);
    };

    const onMouseMove = (e: TPointerEvent) => {
      if (!e.pointer || !ellipse || !startPoint) return;

      const currentPoint = e.pointer;
      const rx = Math.abs(currentPoint.x - startPoint.x) / 2;
      const ry = Math.abs(currentPoint.y - startPoint.y) / 2;
      const left = (startPoint.x + currentPoint.x) / 2;
      const top = (startPoint.y + currentPoint.y) / 2;

      ellipse.set({
        left,
        top,
        rx,
        ry
      });

      this.canvas.requestRenderAll();
    };

    const onMouseUp = () => {
      if (ellipse) {
        this.finalizeEllipseSelection(ellipse, options);
        this.canvas.remove(ellipse);
      }
      this.cleanupSelectionEvents();
    };

    this.canvas.on('mouse:down', onMouseDown);
    this.canvas.on('mouse:move', onMouseMove);
    this.canvas.on('mouse:up', onMouseUp);
  }

  /**
   * Magic wand selection tool with optimized flood-fill algorithm
   */
  private startMagicWandSelection(options: SelectionOptions): void {
    const tolerance = options.tolerance || 10;

    const onMouseDown = async (e: TPointerEvent) => {
      if (!e.pointer) return;

      const startTime = this.performanceEnabled ? performance.now() : 0;

      try {
        const imageData = await this.getCanvasImageData();
        const startPixel = this.getPixelAtPoint(imageData, e.pointer);
        const selection = await this.floodFillSelection(imageData, e.pointer, startPixel, tolerance);
        
        await this.createSelectionFromPixelData(selection, options);
        
        if (this.performanceEnabled) {
          const duration = performance.now() - startTime;
          console.log(`[SelectionManager] Magic wand completed in ${duration.toFixed(2)}ms`);
        }
      } catch (error) {
        console.error('[SelectionManager] Magic wand error:', error);
      this.emitter.emit('selectionError', error);
      }

      this.cleanupSelectionEvents();
    };

    this.canvas.on('mouse:down', onMouseDown);
    this.canvas.defaultCursor = 'crosshair';
  }

  /**
   * Lasso selection tool with freehand drawing
   */
  private startLassoSelection(options: SelectionOptions): void {
    const points: fabric.Point[] = [];
    let isDrawing = false;

    const onMouseDown = (e: TPointerEvent) => {
      if (!e.pointer) return;
      isDrawing = true;
      points.length = 0;
      points.push(e.pointer);
    };

    const onMouseMove = (e: TPointerEvent) => {
      if (!isDrawing || !e.pointer) return;
      points.push(e.pointer);
      this.drawLassoPreview(points);
    };

    const onMouseUp = () => {
      if (isDrawing && points.length > 2) {
        this.finalizeLassoSelection(points, options);
      }
      isDrawing = false;
      this.cleanupSelectionEvents();
    };

    this.canvas.on('mouse:down', onMouseDown);
    this.canvas.on('mouse:move', onMouseMove);
    this.canvas.on('mouse:up', onMouseUp);
    this.canvas.defaultCursor = 'crosshair';
  }

  /**
   * Polygonal lasso selection tool
   */
  private startPolygonalLassoSelection(options: SelectionOptions): void {
    const points: fabric.Point[] = [];
    let polygon: fabric.Polygon;

    const onMouseDown = (e: TPointerEvent) => {
      if (!e.pointer) return;
      
      points.push(e.pointer);
      
      if (points.length === 1) {
        // Start polygon
        polygon = new fabric.Polygon(points, {
          fill: 'transparent',
          stroke: '#007bff',
          strokeWidth: 1,
          strokeDashArray: [5, 5],
          selectable: false,
          evented: false,
          excludeFromExport: true
        });
        this.previewPath = polygon;
        this.canvas.add(polygon);
      } else {
        // Update polygon
        polygon.set('points', points);
        this.canvas.requestRenderAll();
      }
    };

    const onDoubleClick = () => {
      if (points.length > 2) {
        this.finalizePolygonalSelection(points, options);
        this.canvas.remove(polygon);
      }
      this.cleanupSelectionEvents();
    };

    this.canvas.on('mouse:down', onMouseDown);
    this.canvas.on('mouse:dblclick', onDoubleClick);
    this.canvas.defaultCursor = 'crosshair';
  }

  /**
   * Quick mask mode for brush-based selection refinement
   */
  private startQuickMaskMode(options: SelectionOptions): void {
    this.createMaskOverlay();
    
    let isDrawing = false;
    const brushPath: fabric.Point[] = [];
    const brushSize = options.expansion || 20;

    const onMouseDown = (e: TPointerEvent) => {
      if (!e.pointer) return;
      isDrawing = true;
      brushPath.length = 0;
      brushPath.push(e.pointer);
    };

    const onMouseMove = (e: TPointerEvent) => {
      if (!isDrawing || !e.pointer) return;
      brushPath.push(e.pointer);
      this.updateMaskWithBrush(brushPath, brushSize, options);
    };

    const onMouseUp = () => {
      isDrawing = false;
    };

    this.canvas.on('mouse:down', onMouseDown);
    this.canvas.on('mouse:move', onMouseMove);
    this.canvas.on('mouse:up', onMouseUp);
    this.canvas.defaultCursor = 'crosshair';

    // Quick mask specific event handlers
    this.setupQuickMaskKeyboardShortcuts();
  }

  /**
   * Apply selection refinement operations
   */
  refineSelection(operation: 'expand' | 'contract' | 'feather' | 'smooth', amount: number): void {
    if (!this.currentSelection) return;

    const startTime = this.performanceEnabled ? performance.now() : 0;

    switch (operation) {
      case 'expand':
        this.expandSelection(amount);
        break;
      case 'contract':
        this.contractSelection(amount);
        break;
      case 'feather':
        this.featherSelection(amount);
        break;
      case 'smooth':
        this.smoothSelection(amount);
        break;
    }

    if (this.performanceEnabled) {
      const duration = performance.now() - startTime;
      console.log(`[SelectionManager] ${operation} operation completed in ${duration.toFixed(2)}ms`);
    }

      this.emitter.emit('selectionRefined', operation, amount);
  }

  /**
   * Transform current selection
   */
  transformSelection(transform: {
    translation?: { x: number; y: number };
    rotation?: number;
    scale?: { x: number; y: number };
  }): void {
    if (!this.currentSelection || !this.selectionPath) return;

    if (transform.translation) {
      this.selectionPath.set({
        left: (this.selectionPath.left || 0) + transform.translation.x,
        top: (this.selectionPath.top || 0) + transform.translation.y
      });
    }

    if (transform.rotation) {
      this.selectionPath.rotate(transform.rotation);
    }

    if (transform.scale) {
      this.selectionPath.scale(transform.scale.x);
    }

    this.canvas.requestRenderAll();
    this.emitter.emit('selectionTransformed', transform);
  }

  /**
   * Convert selection to layer
   */
  selectionToLayer(layerName?: string): string | null {
    if (!this.currentSelection) return null;

    const selectionImageData = this.extractSelectionImageData();
    const layer = this.layerManager.createLayer(
      layerName || `Selection ${Date.now()}`,
      'image' as any,
      {
        visible: true,
        opacity: 1.0
      }
    );

    // Create fabric image from selection data
    fabric.FabricImage.fromURL(selectionImageData).then((img) => {
      this.layerManager.addObjectToLayer(layer.id, img);
    });

    this.emitter.emit('selectionToLayer', layer.id);
    return layer.id;
  }

  /**
   * Save selection for later use
   */
  saveSelection(name: string): void {
    if (!this.currentSelection) return;

    const savedSelection = {
      ...this.currentSelection,
      name,
      saved: new Date()
    };

    this.selectionHistory.push(savedSelection);
    this.emitter.emit('selectionSaved', [name, savedSelection.id]);
  }

  /**
   * Load previously saved selection
   */
  loadSelection(selectionId: string): boolean {
    const selection = this.selectionHistory.find(s => s.id === selectionId);
    if (!selection) return false;

    this.currentSelection = selection;
    this.recreateSelectionPath();
    this.emitter.emit('selectionLoaded', selectionId);
    return true;
  }

  /**
   * Clear current selection
   */
  clearSelection(): void {
    if (this.selectionPath) {
      this.canvas.remove(this.selectionPath);
      this.selectionPath = null;
    }

    this.clearPreview();
    this.currentSelection = null;
    this.canvas.selection = true; // Re-enable default selection
    this.emitter.emit('selectionCleared');
  }

  /**
   * Get current selection bounds
   */
  getSelectionBounds(): fabric.Rect | null {
    return this.currentSelection?.bounds || null;
  }

  /**
   * Check if point is inside current selection
   */
  isPointInSelection(point: fabric.Point): boolean {
    if (!this.selectionPath) return false;
    return this.selectionPath.containsPoint(point);
  }

  // Private helper methods

  private finalizeRectangleSelection(rectangle: fabric.Rect, options: SelectionOptions): void {
    const bounds = new fabric.Rect({
      left: rectangle.left!,
      top: rectangle.top!,
      width: rectangle.width!,
      height: rectangle.height!
    });

    const pathData = this.rectangleToPath(rectangle);
    this.createSelection(SelectionType.RECTANGLE, pathData, bounds, options);
  }

  private finalizeEllipseSelection(ellipse: fabric.Ellipse, options: SelectionOptions): void {
    const bounds = new fabric.Rect({
      left: (ellipse.left || 0) - (ellipse.rx || 0),
      top: (ellipse.top || 0) - (ellipse.ry || 0),
      width: (ellipse.rx || 0) * 2,
      height: (ellipse.ry || 0) * 2
    });

    const pathData = this.ellipseToPath(ellipse);
    this.createSelection(SelectionType.ELLIPSE, pathData, bounds, options);
  }

  private finalizeLassoSelection(points: fabric.Point[], options: SelectionOptions): void {
    const pathData = this.pointsToPath(points);
    const bounds = this.calculateBounds(points);
    this.createSelection(SelectionType.LASSO, pathData, bounds, options);
  }

  private finalizePolygonalSelection(points: fabric.Point[], options: SelectionOptions): void {
    const pathData = this.pointsToPath(points, true); // closed path
    const bounds = this.calculateBounds(points);
    this.createSelection(SelectionType.POLYGONAL_LASSO, pathData, bounds, options);
  }

  private createSelection(
    type: SelectionType,
    pathData: string,
    bounds: fabric.Rect,
    options: SelectionOptions
  ): void {
    const selection: SelectionData = {
      id: `selection_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type,
      path: pathData,
      bounds,
      featherRadius: options.featherRadius || 0,
      antiAlias: options.antiAlias !== false,
      tolerance: options.tolerance || 10,
      created: new Date(),
      metadata: {}
    };

    // Handle selection operations
    if (options.addToSelection && this.currentSelection) {
      this.addToSelection(selection);
    } else if (options.subtractFromSelection && this.currentSelection) {
      this.subtractFromSelection(selection);
    } else if (options.intersectWithSelection && this.currentSelection) {
      this.intersectWithSelection(selection);
    } else {
      this.currentSelection = selection;
    }

    this.createSelectionPath();
    this.emitter.emit('selectionCreated', this.currentSelection);
  }

  private createSelectionPath(): void {
    if (!this.currentSelection) return;

    if (this.selectionPath) {
      this.canvas.remove(this.selectionPath);
    }

    this.selectionPath = new fabric.Path(this.currentSelection.path, {
      fill: 'transparent',
      stroke: '#007bff',
      strokeWidth: 1,
      strokeDashArray: [5, 5],
      selectable: false,
      evented: false,
      excludeFromExport: true
    });

    this.canvas.add(this.selectionPath);
    this.canvas.requestRenderAll();
  }

  /**
   * Optimized flood-fill algorithm for magic wand selection
   */
  private async floodFillSelection(
    imageData: ImageData,
    startPoint: fabric.Point,
    targetColor: number[],
    tolerance: number
  ): Promise<Uint8ClampedArray> {
    const width = imageData.width;
    const height = imageData.height;
    const visited = new Uint8ClampedArray(width * height);
    const selection = new Uint8ClampedArray(width * height);
    
    const stack: FloodFillPixel[] = [{ x: Math.floor(startPoint.x), y: Math.floor(startPoint.y) }];
    
    // Batch process pixels for better performance
    const batchSize = 1000;
    let processedCount = 0;

    while (stack.length > 0) {
      // Process in batches to prevent blocking
      const batchEnd = Math.min(stack.length, batchSize);
      
      for (let i = 0; i < batchEnd; i++) {
        const point = stack.shift()!;
        const { x, y } = point;
        
        if (x < 0 || x >= width || y < 0 || y >= height) continue;
        
        const index = y * width + x;
        if (visited[index]) continue;
        
        const pixelColor = this.getPixelAtIndex(imageData, index);
        if (!this.colorsMatch(pixelColor, targetColor, tolerance)) continue;
        
        visited[index] = 1;
        selection[index] = 255;
        
        // Add neighboring pixels
        stack.push(
          { x: x + 1, y },
          { x: x - 1, y },
          { x, y: y + 1 },
          { x, y: y - 1 }
        );
      }

      processedCount += batchEnd;

      // Yield control every 10000 pixels to prevent blocking
      if (processedCount > 10000) {
        await new Promise(resolve => setTimeout(resolve, 0));
        processedCount = 0;
      }
    }
    
    return selection;
  }

  private async getCanvasImageData(): Promise<ImageData> {
    // Cache image data for performance
    const now = Date.now();
    if (this.imageDataCache && (now - this.imageDataCacheTime) < this.CACHE_DURATION) {
      return this.imageDataCache;
    }

    const canvasEl = this.canvas.getElement();
    const ctx = canvasEl.getContext('2d')!;
    this.imageDataCache = ctx.getImageData(0, 0, canvasEl.width, canvasEl.height);
    this.imageDataCacheTime = now;
    
    return this.imageDataCache;
  }

  private getPixelAtPoint(imageData: ImageData, point: fabric.Point): number[] {
    const x = Math.floor(point.x);
    const y = Math.floor(point.y);
    const index = (y * imageData.width + x) * 4;
    
    return [
      imageData.data[index],     // R
      imageData.data[index + 1], // G
      imageData.data[index + 2], // B
      imageData.data[index + 3]  // A
    ];
  }

  private getPixelAtIndex(imageData: ImageData, pixelIndex: number): number[] {
    const index = pixelIndex * 4;
    return [
      imageData.data[index],     // R
      imageData.data[index + 1], // G
      imageData.data[index + 2], // B
      imageData.data[index + 3]  // A
    ];
  }

  private colorsMatch(color1: number[], color2: number[], tolerance: number): boolean {
    const dr = Math.abs(color1[0] - color2[0]);
    const dg = Math.abs(color1[1] - color2[1]);
    const db = Math.abs(color1[2] - color2[2]);
    const da = Math.abs(color1[3] - color2[3]);
    
    return (dr + dg + db + da) / 4 <= tolerance;
  }

  private async createSelectionFromPixelData(
    pixelData: Uint8ClampedArray,
    options: SelectionOptions
  ): Promise<void> {
    // Convert pixel data to path using marching squares algorithm
    const pathData = await this.pixelDataToPath(pixelData, this.canvas.getWidth(), this.canvas.getHeight());
    
    if (pathData) {
      const bounds = this.calculatePathBounds(pathData);
      this.createSelection(SelectionType.MAGIC_WAND, pathData, bounds, options);
    }
  }

  private async pixelDataToPath(pixelData: Uint8ClampedArray, width: number, height: number): Promise<string | null> {
    // Simplified marching squares implementation
    // For production, consider using a proper library like potrace
    const paths: string[] = [];
    
    for (let y = 0; y < height - 1; y++) {
      for (let x = 0; x < width - 1; x++) {
        const tl = pixelData[y * width + x] > 128 ? 1 : 0;
        const tr = pixelData[y * width + x + 1] > 128 ? 1 : 0;
        const bl = pixelData[(y + 1) * width + x] > 128 ? 1 : 0;
        const br = pixelData[(y + 1) * width + x + 1] > 128 ? 1 : 0;
        
        const config = tl * 8 + tr * 4 + br * 2 + bl;
        
        // Generate path segments based on marching squares configuration
        const segment = this.getMarchingSquareSegment(x, y, config);
        if (segment) {
          paths.push(segment);
        }
      }
    }
    
    return paths.length > 0 ? paths.join(' ') : null;
  }

  private getMarchingSquareSegment(x: number, y: number, config: number): string | null {
    // Simplified marching squares lookup table
    const segments: { [key: number]: string } = {
      1: `M ${x} ${y + 0.5} L ${x + 0.5} ${y + 1}`,
      2: `M ${x + 0.5} ${y + 1} L ${x + 1} ${y + 0.5}`,
      3: `M ${x} ${y + 0.5} L ${x + 1} ${y + 0.5}`,
      4: `M ${x + 1} ${y + 0.5} L ${x + 0.5} ${y}`,
      // ... add more configurations as needed
    };
    
    return segments[config] || null;
  }

  private setupCanvasEvents(): void {
    // Keyboard shortcuts for selection operations
    document.addEventListener('keydown', (e) => {
      if (!this.currentSelection) return;

      switch (e.key) {
        case 'Delete':
        case 'Backspace':
          this.clearSelection();
          break;
        case 'Escape':
          if (this.isSelecting) {
            this.cancelSelection();
          }
          break;
      }
    });
  }

  private cleanupSelectionEvents(): void {
    this.canvas.off('mouse:down');
    this.canvas.off('mouse:move');
    this.canvas.off('mouse:up');
    this.canvas.off('mouse:dblclick');
    this.canvas.defaultCursor = 'default';
    this.isSelecting = false;
    this.clearPreview();
  }

  private clearPreview(): void {
    if (this.previewPath) {
      this.canvas.remove(this.previewPath);
      this.previewPath = null;
    }
  }

  private cancelSelection(): void {
    this.isSelecting = false;
    this.cleanupSelectionEvents();
    this.emitter.emit('selectionCancelled');
  }

  // Selection refinement methods
  private expandSelection(amount: number): void {
    if (!this.currentSelection) return;
    
    // Apply morphological dilation to expand selection
    const expandedPath = this.morphologyOperation(this.currentSelection.path, 'dilate', amount);
    this.currentSelection.path = expandedPath;
    this.recreateSelectionPath();
  }

  private contractSelection(amount: number): void {
    if (!this.currentSelection) return;
    
    // Apply morphological erosion to contract selection
    const contractedPath = this.morphologyOperation(this.currentSelection.path, 'erode', amount);
    this.currentSelection.path = contractedPath;
    this.recreateSelectionPath();
  }

  private featherSelection(amount: number): void {
    if (!this.currentSelection) return;
    this.currentSelection.featherRadius = amount;
    this.recreateSelectionPath();
  }

  private smoothSelection(amount: number): void {
    if (!this.currentSelection) return;
    
    // Apply path smoothing algorithm
    const smoothedPath = this.smoothPath(this.currentSelection.path, amount);
    this.currentSelection.path = smoothedPath;
    this.recreateSelectionPath();
  }

  // Path operation methods
  private rectangleToPath(rectangle: fabric.Rect): string {
    const left = rectangle.left!;
    const top = rectangle.top!;
    const width = rectangle.width!;
    const height = rectangle.height!;

    return `M ${left} ${top} L ${left + width} ${top} L ${left + width} ${top + height} L ${left} ${top + height} Z`;
  }

  private ellipseToPath(ellipse: fabric.Ellipse): string {
    const cx = ellipse.left!;
    const cy = ellipse.top!;
    const rx = ellipse.rx!;
    const ry = ellipse.ry!;

    return `M ${cx - rx} ${cy} A ${rx} ${ry} 0 1 1 ${cx + rx} ${cy} A ${rx} ${ry} 0 1 1 ${cx - rx} ${cy} Z`;
  }

  private pointsToPath(points: fabric.Point[], closed: boolean = false): string {
    if (points.length === 0) return '';

    let path = `M ${points[0].x} ${points[0].y}`;
    for (let i = 1; i < points.length; i++) {
      path += ` L ${points[i].x} ${points[i].y}`;
    }

    if (closed) {
      path += ' Z';
    }

    return path;
  }

  private calculateBounds(points: fabric.Point[]): fabric.Rect {
    const xs = points.map(p => p.x);
    const ys = points.map(p => p.y);
    
    const minX = Math.min(...xs);
    const maxX = Math.max(...xs);
    const minY = Math.min(...ys);
    const maxY = Math.max(...ys);

    return new fabric.Rect({
      left: minX,
      top: minY,
      width: maxX - minX,
      height: maxY - minY
    });
  }

  private calculatePathBounds(pathData: string): fabric.Rect {
    // Parse path data to calculate bounds
    // This is a simplified implementation
    const coords = pathData.match(/[\d.]+/g)?.map(Number) || [];
    const xs = coords.filter((_, i) => i % 2 === 0);
    const ys = coords.filter((_, i) => i % 2 === 1);
    
    if (xs.length === 0 || ys.length === 0) {
      return new fabric.Rect({ left: 0, top: 0, width: 0, height: 0 });
    }
    
    const minX = Math.min(...xs);
    const maxX = Math.max(...xs);
    const minY = Math.min(...ys);
    const maxY = Math.max(...ys);

    return new fabric.Rect({
      left: minX,
      top: minY,
      width: maxX - minX,
      height: maxY - minY
    });
  }

  private recreateSelectionPath(): void {
    this.createSelectionPath();
  }

  private extractSelectionImageData(): string {
    // Extract image data within selection bounds and return as data URL
    const bounds = this.currentSelection!.bounds;
    const canvasEl = this.canvas.getElement();
    const ctx = canvasEl.getContext('2d')!;
    
    const imageData = ctx.getImageData(
      bounds.left!,
      bounds.top!,
      bounds.width!,
      bounds.height!
    );

    // Create temporary canvas for extracted data
    const tempCanvas = document.createElement('canvas');
    tempCanvas.width = bounds.width!;
    tempCanvas.height = bounds.height!;
    const tempCtx = tempCanvas.getContext('2d')!;
    tempCtx.putImageData(imageData, 0, 0);
    
    return tempCanvas.toDataURL();
  }

  // Selection combination operations
  private addToSelection(newSelection: SelectionData): void {
    // Implement selection addition using path operations
    // This would require a path union algorithm
    console.log('Add to selection - implementation needed');
  }

  private subtractFromSelection(selectionToSubtract: SelectionData): void {
    // Implement selection subtraction using path operations
    console.log('Subtract from selection - implementation needed');
  }

  private intersectWithSelection(selectionToIntersect: SelectionData): void {
    // Implement selection intersection using path operations
    console.log('Intersect with selection - implementation needed');
  }

  // Quick mask mode methods
  private createMaskOverlay(): void {
    this.maskOverlay = new fabric.Rect({
      left: 0,
      top: 0,
      width: this.canvas.getWidth(),
      height: this.canvas.getHeight(),
      fill: 'rgba(255, 0, 0, 0.5)', // Red mask overlay
      selectable: false,
      evented: false,
      excludeFromExport: true
    });
    
    this.canvas.add(this.maskOverlay);
  }

  private updateMaskWithBrush(brushPath: fabric.Point[], brushSize: number, options: SelectionOptions): void {
    if (!this.maskContext || !this.maskCanvas) return;

    // Draw brush strokes on mask canvas
    this.maskContext.globalCompositeOperation = options.subtractFromSelection ? 'destination-out' : 'source-over';
    this.maskContext.fillStyle = 'white';
    
    for (const point of brushPath) {
      this.maskContext.beginPath();
      this.maskContext.arc(point.x, point.y, brushSize / 2, 0, Math.PI * 2);
      this.maskContext.fill();
    }
  }

  private setupQuickMaskKeyboardShortcuts(): void {
    const keyHandler = (e: KeyboardEvent) => {
      switch (e.key) {
        case 'Enter':
          this.exitQuickMaskMode();
          break;
        case 'Escape':
          this.cancelQuickMaskMode();
          break;
      }
    };

    document.addEventListener('keydown', keyHandler);
    
    // Store handler for cleanup
    (this.maskOverlay as any).__keyHandler = keyHandler;
  }

  private exitQuickMaskMode(): void {
    // Convert mask to selection
    this.convertMaskToSelection();
    if (this.maskOverlay) {
      this.canvas.remove(this.maskOverlay);
    }
    this.cleanupQuickMaskEvents();
  }

  private cancelQuickMaskMode(): void {
    if (this.maskOverlay) {
      this.canvas.remove(this.maskOverlay);
    }
    this.cleanupQuickMaskEvents();
    this.emitter.emit('quickMaskCancelled');
  }

  // Event listener methods for compatibility with SelectionToolsPanel
  on<Key extends keyof Events>(type: Key, handler: (event: Events[Key]) => void): void {
    this.emitter.on(type, handler);
  }

  off<Key extends keyof Events>(type: Key, handler?: (event: Events[Key]) => void): void {
    if (handler) {
      this.emitter.off(type, handler);
    } else {
      // If no handler specified, remove all handlers for this event
      this.emitter.all.delete(type);
    }
  }

  emit<Key extends keyof Events>(type: Key, event: Events[Key]): void {
    this.emitter.emit(type, event);
  }

  private cleanupQuickMaskEvents(): void {
    const keyHandler = (this.maskOverlay as any)?.__keyHandler;
    if (keyHandler) {
      document.removeEventListener('keydown', keyHandler);
    }
    this.cleanupSelectionEvents();
    this.maskOverlay = null;
  }

  private convertMaskToSelection(): void {
    // Convert mask overlay to selection path
    // This would involve analyzing the mask's alpha channel
    console.log('Convert mask to selection - implementation needed');
  }

  private drawLassoPreview(points: fabric.Point[]): void {
    // Remove previous preview
    this.clearPreview();

    if (points.length < 2) return;

    // Create preview line
    const path = new fabric.Path(this.pointsToPath(points), {
      fill: 'transparent',
      stroke: '#007bff',
      strokeWidth: 1,
      selectable: false,
      evented: false,
      excludeFromExport: true
    });

    this.previewPath = path;
    this.canvas.add(path);
    this.canvas.requestRenderAll();
  }

  // Morphology and path operations (simplified implementations)
  private morphologyOperation(pathData: string, operation: 'dilate' | 'erode', amount: number): string {
    // Simplified morphology - in production, use proper algorithms
    return pathData;
  }

  private smoothPath(pathData: string, amount: number): string {
    // Simplified path smoothing - in production, use proper curve fitting
    return pathData;
  }

  /**
   * Get all saved selections
   */
  getSavedSelections(): SelectionData[] {
    return [...this.selectionHistory];
  }

  /**
   * Delete saved selection
   */
  deleteSavedSelection(selectionId: string): boolean {
    const index = this.selectionHistory.findIndex(s => s.id === selectionId);
    if (index >= 0) {
      this.selectionHistory.splice(index, 1);
      this.emitter.emit('selectionDeleted', selectionId);
      return true;
    }
    return false;
  }

  /**
   * Get current selection data
   */
  getCurrentSelection(): SelectionData | null {
    return this.currentSelection;
  }

  /**
   * Check if selection is active
   */
  hasSelection(): boolean {
    return this.currentSelection !== null;
  }

  /**
   * Cleanup resources
   */
  dispose(): void {
    this.clearSelection();
    // No equivalent method in mitt; manually manage listeners if needed
    
    if (this.maskCanvas) {
      this.maskCanvas = null;
      this.maskContext = null;
    }
    
    this.selectionHistory = [];
    this.imageDataCache = null;
  }
}
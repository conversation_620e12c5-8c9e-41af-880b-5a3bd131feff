import type { Canvas, FabricObject, IEvent } from "fabric";
import * as fabric from "fabric";
import mitt from 'mitt';

type LayerEvents = {
  layerCreated: LayerData;
  objectAddedToLayer: [string, fabric.FabricObject];
  layerVisibilityChanged: [string, boolean];
  layerOpacityChanged: [string, number];
  layerBlendModeChanged: [string, BlendMode];
  layerLockChanged: [string, boolean];
  layerRenamed: [string, string];
  layersReordered: string[];
  layerEffectAdded: [string, LayerEffect];
  layerEffectRemoved: [string, string];
  layerThumbnailGenerated: [string, string];
  activeLayerChanged: string | null;
  layerDeleted: string;
  layersImported: any;
};

export interface LayerData {
  id: string;
  name: string;
  type: LayerType;
  visible: boolean;
  locked: boolean;
  opacity: number;
  blendMode: BlendMode;
  parentId?: string;
  order: number;
  thumbnail?: string;
  effects: LayerEffect[];
  mask?: LayerMask;
  smartObject?: SmartObjectData;
}

export enum LayerType {
  IMAGE = "image",
  TEXT = "text",
  SHAPE = "shape",
  GROUP = "group",
  ADJUSTMENT = "adjustment",
  MASK = "mask",
  SMART_OBJECT = "smart_object",
}

export enum BlendMode {
  NORMAL = "normal",
  MULTIPLY = "multiply",
  SCREEN = "screen",
  OVERLAY = "overlay",
  SOFT_LIGHT = "soft-light",
  HARD_LIGHT = "hard-light",
  COLOR_DODGE = "color-dodge",
  COLOR_BURN = "color-burn",
  DARKEN = "darken",
  LIGHTEN = "lighten",
  DIFFERENCE = "difference",
  EXCLUSION = "exclusion",
}

export interface LayerEffect {
  type: "drop-shadow" | "inner-shadow" | "outer-glow" | "inner-glow" | "blur";
  enabled: boolean;
  properties: Record<string, any>;
}

export interface LayerMask {
  type: "alpha" | "vector";
  enabled: boolean;
  inverted: boolean;
  maskObject?: fabric.FabricObject;
}

export interface SmartObjectData {
  originalData: string;
  originalWidth: number;
  originalHeight: number;
  transformations: any[];
}

export class LayerManager {
  private emitter = mitt<LayerEvents>();
  private canvas: Canvas;
  private layers: Map<string, LayerData> = new Map();
  private fabricObjects: Map<string, fabric.FabricObject> = new Map();
  private layerOrder: string[] = [];
  private activeLayerId: string | null = null;
  private thumbnailCanvas: fabric.Canvas | null = null;
  private disposed: boolean = false;

  constructor(canvas: Canvas) {
    this.canvas = canvas;
    this.setupCanvasEvents();
    this.initializeThumbnailCanvas();
  }

  /**
   * Initialize thumbnail generation canvas
   */
  private initializeThumbnailCanvas(): void {
    // Create a static canvas for thumbnail generation
    const canvasEl = document.createElement("canvas");
    canvasEl.width = 64;
    canvasEl.height = 64;
    this.thumbnailCanvas = new fabric.Canvas(canvasEl, {
      renderOnAddRemove: false,
      selection: false,
    });
  }

  /**
   * Create a new layer with specified type and properties
   */
  createLayer(
    name: string,
    type: LayerType,
    options: Partial<LayerData> = {},
  ): LayerData {
    const layerId = this.generateLayerId();

    const layer: LayerData = {
      id: layerId,
      name: name || `${type} ${this.layers.size + 1}`,
      type,
      visible: true,
      locked: false,
      opacity: 1.0,
      blendMode: BlendMode.NORMAL,
      order: this.layerOrder.length,
      effects: [],
      ...options,
    };

    this.layers.set(layerId, layer);
    this.layerOrder.push(layerId);

    // Generate thumbnail after object is added
    setTimeout(() => this.generateLayerThumbnail(layerId), 100);

    this.emitter.emit("layerCreated", layer);
    return layer;
  }

  /**
   * Add Fabric.js object to layer
   */
  addObjectToLayer(layerId: string, object: fabric.FabricObject): void {
    const layer = this.layers.get(layerId);
    if (!layer) throw new Error(`Layer ${layerId} not found`);

    // Set layer properties on Fabric object
    (object as any).layerId = layerId;
    object.set({
      selectable: !layer.locked,
      evented: !layer.locked,
      visible: layer.visible,
      opacity: layer.opacity,
    });

    // Apply blend mode
    this.applyBlendMode(object, layer.blendMode);

    // Apply layer effects
    this.applyLayerEffects(object, layer.effects);

    this.fabricObjects.set(layerId, object);
    this.canvas.add(object);

    // Update canvas ordering
    this.updateCanvasOrdering();

    // Generate thumbnail
    this.generateLayerThumbnail(layerId);

    this.emitter.emit("objectAddedToLayer", [layerId, object]);
  }

  /**
   * Create layer from existing Fabric object
   */
  createLayerFromObject(
    object: fabric.FabricObject,
    name?: string,
  ): LayerData {
    // Determine layer type from object type
    let layerType = LayerType.SHAPE;
    if (object instanceof fabric.FabricImage) {
      layerType = LayerType.IMAGE;
    } else if (object instanceof fabric.FabricText) {
      layerType = LayerType.TEXT;
    } else if (object instanceof fabric.Group) {
      layerType = LayerType.GROUP;
    }

    const layer = this.createLayer(name || `Layer ${this.layers.size + 1}`, layerType);
    
    // Assign layer ID to object
    (object as any).layerId = layer.id;
    
    // Store object reference
    this.fabricObjects.set(layer.id, object);
    
    // Update canvas ordering
    this.updateCanvasOrdering();
    
    // Generate thumbnail
    this.generateLayerThumbnail(layer.id);
    
    return layer;
  }

  /**
   * Set layer visibility
   */
  setLayerVisibility(layerId: string, visible: boolean): void {
    const layer = this.layers.get(layerId);
    if (!layer) return;

    layer.visible = visible;

    const object = this.fabricObjects.get(layerId);
    if (object) {
      object.set("visible", visible);
      this.canvas.requestRenderAll();
    }

    this.emitter.emit("layerVisibilityChanged", [layerId, visible]);
  }

  /**
   * Set layer opacity
   */
  setLayerOpacity(layerId: string, opacity: number): void {
    const layer = this.layers.get(layerId);
    if (!layer) return;

    layer.opacity = Math.max(0, Math.min(1, opacity));

    const object = this.fabricObjects.get(layerId);
    if (object) {
      object.set("opacity", layer.opacity);
      this.canvas.requestRenderAll();
    }

    this.emitter.emit("layerOpacityChanged", [layerId, layer.opacity]);
  }

  /**
   * Set layer blend mode
   */
  setLayerBlendMode(layerId: string, blendMode: BlendMode): void {
    const layer = this.layers.get(layerId);
    if (!layer) return;

    layer.blendMode = blendMode;

    const object = this.fabricObjects.get(layerId);
    if (object) {
      this.applyBlendMode(object, blendMode);
      this.canvas.requestRenderAll();
    }

    this.emitter.emit("layerBlendModeChanged", [layerId, blendMode]);
  }

  /**
   * Lock/unlock layer
   */
  setLayerLocked(layerId: string, locked: boolean): void {
    const layer = this.layers.get(layerId);
    if (!layer) return;

    layer.locked = locked;

    const object = this.fabricObjects.get(layerId);
    if (object) {
      object.set({
        selectable: !locked,
        evented: !locked,
      });
      
      // Deselect if locking active object
      if (locked && this.canvas.getActiveObject() === object) {
        this.canvas.discardActiveObject();
      }
      
      this.canvas.requestRenderAll();
    }

    this.emitter.emit("layerLockChanged", [layerId, locked]);
  }

  /**
   * Rename layer
   */
  renameLayer(layerId: string, name: string): void {
    const layer = this.layers.get(layerId);
    if (!layer) return;

    layer.name = name;
    this.emitter.emit("layerRenamed", [layerId, name]);
  }

  /**
   * Reorder layers via drag and drop
   */
  reorderLayers(fromIndex: number, toIndex: number): void {
    if (fromIndex === toIndex) return;
    
    const [movedLayer] = this.layerOrder.splice(fromIndex, 1);
    this.layerOrder.splice(toIndex, 0, movedLayer);

    // Update order property
    this.layerOrder.forEach((layerId, index) => {
      const layer = this.layers.get(layerId);
      if (layer) layer.order = index;
    });

    this.updateCanvasOrdering();
    this.emitter.emit("layersReordered", this.layerOrder);
  }

  /**
   * Duplicate layer
   */
  async duplicateLayer(layerId: string): Promise<LayerData> {
    const originalLayer = this.layers.get(layerId);
    if (!originalLayer) throw new Error(`Layer ${layerId} not found`);

    const originalObject = this.fabricObjects.get(layerId);
    if (!originalObject) throw new Error(`Object for layer ${layerId} not found`);

    // Clone the Fabric object
    const clonedObject = await this.cloneFabricObject(originalObject);

    // Create new layer
    const newLayer = this.createLayer(
      `${originalLayer.name} Copy`,
      originalLayer.type,
      {
        visible: originalLayer.visible,
        opacity: originalLayer.opacity,
        blendMode: originalLayer.blendMode,
        effects: [...originalLayer.effects],
      },
    );

    // Position cloned object slightly offset
    clonedObject.set({
      left: (clonedObject.left || 0) + 20,
      top: (clonedObject.top || 0) + 20,
    });

    // Add cloned object to new layer
    this.addObjectToLayer(newLayer.id, clonedObject);

    return newLayer;
  }

  /**
   * Merge layers
   */
  async mergeLayers(layerIds: string[]): Promise<LayerData> {
    if (layerIds.length < 2) throw new Error("Need at least 2 layers to merge");

    const layersToMerge = layerIds
      .map((id) => this.layers.get(id))
      .filter(Boolean) as LayerData[];

    // Get objects to merge
    const objectsToMerge = layerIds
      .map((id) => this.fabricObjects.get(id))
      .filter(Boolean) as fabric.FabricObject[];

    if (objectsToMerge.length === 0) {
      throw new Error("No objects found to merge");
    }

    // Create a group from the objects
    const group = new fabric.Group(objectsToMerge, {
      canvas: this.canvas,
    });

    // Create new merged layer
    const mergedLayer = this.createLayer("Merged Layer", LayerType.GROUP);

    // Remove original objects from canvas without destroying them
    objectsToMerge.forEach((obj) => {
      this.canvas.remove(obj);
    });

    // Add group to the new layer
    this.addObjectToLayer(mergedLayer.id, group);

    // Remove original layers
    layerIds.forEach((id) => this.deleteLayer(id, false)); // false = don't remove from canvas

    return mergedLayer;
  }

  /**
   * Add layer effect
   */
  addLayerEffect(layerId: string, effect: LayerEffect): void {
    const layer = this.layers.get(layerId);
    if (!layer) return;

    // Check if effect already exists
    const existingIndex = layer.effects.findIndex((e) => e.type === effect.type);
    if (existingIndex >= 0) {
      layer.effects[existingIndex] = effect;
    } else {
      layer.effects.push(effect);
    }

    const object = this.fabricObjects.get(layerId);
    if (object) {
      this.applyLayerEffects(object, layer.effects);
      this.canvas.requestRenderAll();
    }

    this.emitter.emit("layerEffectAdded", [layerId, effect]);
  }

  /**
   * Remove layer effect
   */
  removeLayerEffect(layerId: string, effectType: string): void {
    const layer = this.layers.get(layerId);
    if (!layer) return;

    layer.effects = layer.effects.filter((e) => e.type !== effectType);

    const object = this.fabricObjects.get(layerId);
    if (object) {
      this.applyLayerEffects(object, layer.effects);
      this.canvas.requestRenderAll();
    }

    this.emitter.emit("layerEffectRemoved", [layerId, effectType]);
  }

  /**
   * Apply layer effects
   */
  private applyLayerEffects(
    object: fabric.FabricObject,
    effects: LayerEffect[],
  ): void {
    // Reset shadow first
    object.set("shadow", null);

    // Apply enabled effects
    effects.forEach((effect) => {
      if (!effect.enabled) return;

      switch (effect.type) {
        case "drop-shadow":
          object.set(
            "shadow",
            new fabric.Shadow({
              color: effect.properties.color || "rgba(0,0,0,0.5)",
              blur: effect.properties.blur || 5,
              offsetX: effect.properties.offsetX || 5,
              offsetY: effect.properties.offsetY || 5,
            }),
          );
          break;

        case "blur":
          if (object instanceof fabric.FabricImage) {
            const blurFilter = new fabric.filters.Blur({
              blur: effect.properties.radius || 0.1,
            });
            object.filters = object.filters || [];
            object.filters.push(blurFilter);
            object.applyFilters();
          }
          break;
      }
    });
  }

  /**
   * Apply blend mode using globalCompositeOperation
   */
  private applyBlendMode(
    object: fabric.FabricObject,
    blendMode: BlendMode,
  ): void {
    // Map blend modes to canvas composite operations
    const compositeOperationMap: Record<BlendMode, GlobalCompositeOperation> = {
      [BlendMode.NORMAL]: "source-over",
      [BlendMode.MULTIPLY]: "multiply",
      [BlendMode.SCREEN]: "screen",
      [BlendMode.OVERLAY]: "overlay",
      [BlendMode.SOFT_LIGHT]: "soft-light",
      [BlendMode.HARD_LIGHT]: "hard-light",
      [BlendMode.COLOR_DODGE]: "color-dodge",
      [BlendMode.COLOR_BURN]: "color-burn",
      [BlendMode.DARKEN]: "darken",
      [BlendMode.LIGHTEN]: "lighten",
      [BlendMode.DIFFERENCE]: "difference",
      [BlendMode.EXCLUSION]: "exclusion",
    };

    (object as any).globalCompositeOperation = compositeOperationMap[blendMode] || "source-over";
  }

  /**
   * Generate layer thumbnail
   */
  private async generateLayerThumbnail(layerId: string): Promise<void> {
    if (!this.thumbnailCanvas || this.disposed) return;

    const object = this.fabricObjects.get(layerId);
    if (!object) return;

    try {
      // Clear thumbnail canvas
      this.thumbnailCanvas.clear();

      // Clone object for thumbnail
      const thumbnailObject = await this.cloneFabricObject(object);

      // Calculate scale to fit
      const bounds = thumbnailObject.getBoundingRect();
      const scale = Math.min(
        56 / bounds.width,
        56 / bounds.height,
        1, // Don't scale up
      );

      // Center and scale object
      thumbnailObject.set({
        scaleX: (thumbnailObject.scaleX || 1) * scale,
        scaleY: (thumbnailObject.scaleY || 1) * scale,
        left: 32,
        top: 32,
        originX: "center",
        originY: "center",
      });

      this.thumbnailCanvas.add(thumbnailObject);
      this.thumbnailCanvas.renderAll();

      const layer = this.layers.get(layerId);
      if (layer) {
        layer.thumbnail = this.thumbnailCanvas.toDataURL();
        this.emitter.emit("layerThumbnailGenerated", [layerId, layer.thumbnail]);
      }

      // Clean up
      this.thumbnailCanvas.remove(thumbnailObject);
    } catch (error) {
      console.error("Error generating thumbnail:", error);
    }
  }

  /**
   * Update canvas object ordering based on layer order
   */
  private updateCanvasOrdering(): void {
    // Create array of objects in correct order
    const orderedObjects: fabric.FabricObject[] = [];
    
    for (const layerId of this.layerOrder) {
      const object = this.fabricObjects.get(layerId);
      if (object) {
        orderedObjects.push(object);
      }
    }

    // Remove all objects and re-add in correct order
    this.canvas.discardActiveObject();
    orderedObjects.forEach((object) => {
      this.canvas.remove(object);
    });
    orderedObjects.forEach((object) => {
      this.canvas.add(object);
    });
    
    this.canvas.requestRenderAll();
  }

  /**
   * Clone Fabric.js object
   */
  private async cloneFabricObject(
    object: fabric.FabricObject,
  ): Promise<fabric.FabricObject> {
    return new Promise((resolve, reject) => {
      object.clone((cloned: fabric.FabricObject) => {
        if (cloned) {
          resolve(cloned);
        } else {
          reject(new Error("Failed to clone object"));
        }
      });
    });
  }

  /**
   * Setup canvas event listeners
   */
  private setupCanvasEvents(): void {
    this.canvas.on("selection:created", (e: IEvent<MouseEvent>) => {
      const selectedObject = e.selected?.[0];
      if (selectedObject && (selectedObject as any).layerId) {
        this.setActiveLayer((selectedObject as any).layerId);
      }
    });

    this.canvas.on("selection:updated", (e: IEvent<MouseEvent>) => {
      const selectedObject = e.selected?.[0];
      if (selectedObject && (selectedObject as any).layerId) {
        this.setActiveLayer((selectedObject as any).layerId);
      }
    });

    this.canvas.on("selection:cleared", () => {
      this.setActiveLayer(null);
    });

    this.canvas.on("object:modified", (e: IEvent<MouseEvent>) => {
      const object = e.target;
      if (object && (object as any).layerId) {
        this.generateLayerThumbnail((object as any).layerId);
      }
    });

    // Handle objects added outside layer manager
    this.canvas.on("object:added", (e: IEvent<MouseEvent>) => {
      const object = e.target;
      if (object && !(object as any).layerId) {
        // Create a layer for objects added directly to canvas
        this.createLayerFromObject(object);
      }
    });
  }

  /**
   * Generate unique layer ID
   */
  private generateLayerId(): string {
    return `layer_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Public API methods
  setActiveLayer(layerId: string | null): void {
    this.activeLayerId = layerId;
    this.emitter.emit("activeLayerChanged", layerId);
  }

  getActiveLayer(): LayerData | null {
    return this.activeLayerId
      ? this.layers.get(this.activeLayerId) || null
      : null;
  }

  deleteLayer(layerId: string, removeFromCanvas: boolean = true): void {
    const object = this.fabricObjects.get(layerId);
    if (object && removeFromCanvas) {
      this.canvas.remove(object);
    }
    this.fabricObjects.delete(layerId);

    this.layers.delete(layerId);
    this.layerOrder = this.layerOrder.filter((id) => id !== layerId);

    if (this.activeLayerId === layerId) {
      this.setActiveLayer(null);
    }

    this.emitter.emit("layerDeleted", layerId);
  }

  getAllLayers(): LayerData[] {
    return this.layerOrder
      .map((id) => this.layers.get(id)!)
      .filter(Boolean);
  }

  getLayerById(layerId: string): LayerData | undefined {
    return this.layers.get(layerId);
  }

  getObjectByLayerId(layerId: string): fabric.FabricObject | undefined {
    return this.fabricObjects.get(layerId);
  }

  /**
   * Initialize layers from existing canvas objects
   */
  initializeFromCanvas(): void {
    const objects = this.canvas.getObjects();
    objects.forEach((object, index) => {
      if (!(object as any).layerId) {
        this.createLayerFromObject(object, `Layer ${index + 1}`);
      }
    });
  }

  /**
   * Export layer data for persistence
   */
  exportLayerData(): any {
    return {
      layers: Array.from(this.layers.values()),
      layerOrder: this.layerOrder,
      activeLayerId: this.activeLayerId,
    };
  }

  /**
   * Import layer data
   */
  importLayerData(data: any): void {
    if (data.layers) {
      this.layers.clear();
      data.layers.forEach((layer: LayerData) => {
        this.layers.set(layer.id, layer);
      });
    }
    
    if (data.layerOrder) {
      this.layerOrder = data.layerOrder;
    }
    
    if (data.activeLayerId) {
      this.activeLayerId = data.activeLayerId;
    }
    
    this.emitter.emit("layersImported", data);
  }

  // Event listener methods for compatibility
  on<Key extends keyof LayerEvents>(type: Key, handler: (event: LayerEvents[Key]) => void): void {
    this.emitter.on(type, handler);
  }

  off<Key extends keyof LayerEvents>(type: Key, handler?: (event: LayerEvents[Key]) => void): void {
    if (handler) {
      this.emitter.off(type, handler);
    } else {
      // If no handler specified, remove all handlers for this event
      this.emitter.all.delete(type);
    }
  }

  emit<Key extends keyof LayerEvents>(type: Key, event: LayerEvents[Key]): void {
    this.emitter.emit(type, event);
  }

  /**
   * Cleanup resources
   */
  dispose(): void {
    this.disposed = true;
    
    if (this.thumbnailCanvas) {
      this.thumbnailCanvas.dispose();
      this.thumbnailCanvas = null;
    }
    
    // No equivalent method in mitt; manually manage listeners if needed
    this.layers.clear();
    this.fabricObjects.clear();
    this.layerOrder = [];
    this.activeLayerId = null;
  }
}
// TemplateManager.ts - Manages batch processing templates
import mitt from 'mitt';
import { RecordedAction, ActionType } from './ActionRecorder';
import { BatchTemplate, ExportSettings } from './BatchProcessor';

export interface TemplateCategory {
  id: string;
  name: string;
  description: string;
  color: string;
  icon: string;
}

export interface TemplateStats {
  totalTemplates: number;
  totalActions: number;
  mostUsedActionTypes: Array<{ type: ActionType; count: number }>;
  averageActionsPerTemplate: number;
  templatesByCategory: Map<string, number>;
  recentlyUsed: BatchTemplate[];
  popularTemplates: Array<{ template: BatchTemplate; usageCount: number }>;
}

export interface TemplateSearchOptions {
  query?: string;
  tags?: string[];
  categories?: string[];
  actionTypes?: ActionType[];
  author?: string;
  dateRange?: {
    start: Date;
    end: Date;
  };
  sortBy?: 'name' | 'created' | 'modified' | 'usage' | 'actions';
  sortOrder?: 'asc' | 'desc';
  limit?: number;
}

export interface TemplateValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  suggestions: string[];
}

type TemplateEvents = {
  templateSaved: BatchTemplate;
  templateUpdated: BatchTemplate;
  templateDuplicated: [BatchTemplate, BatchTemplate];
  templateDeleted: [string, BatchTemplate];
  templateImported: BatchTemplate;
  categorySaved: TemplateCategory;
  categoryDeleted: string;
  allTemplatesCleared: void;
}

export class TemplateManager {
  private templates: Map<string, BatchTemplate> = new Map();
  private categories: Map<string, TemplateCategory> = new Map();
  private templateUsage: Map<string, number> = new Map();
  private recentlyUsed: string[] = [];
  private localStorage: Storage;
  private readonly STORAGE_KEY = 'photo_editor_batch_templates';
  private readonly CATEGORIES_KEY = 'photo_editor_template_categories';
  private readonly USAGE_KEY = 'photo_editor_template_usage';

  constructor() {
    this.emitter = mitt<TemplateEvents>();
    this.localStorage = window.localStorage;
    this.initializeDefaultCategories();
    this.loadFromStorage();
  }

  /**
   * Initialize default template categories
   */
  private initializeDefaultCategories(): void {
    const defaultCategories: TemplateCategory[] = [
      {
        id: 'social-media',
        name: 'Social Media',
        description: 'Templates optimized for social media platforms',
        color: '#3b82f6',
        icon: 'share-2'
      },
      {
        id: 'print',
        name: 'Print Ready',
        description: 'High-quality templates for print media',
        color: '#10b981',
        icon: 'printer'
      },
      {
        id: 'web',
        name: 'Web Optimization',
        description: 'Templates optimized for web use',
        color: '#f59e0b',
        icon: 'globe'
      },
      {
        id: 'professional',
        name: 'Professional',
        description: 'Advanced templates for professional workflows',
        color: '#8b5cf6',
        icon: 'briefcase'
      },
      {
        id: 'creative',
        name: 'Creative Effects',
        description: 'Artistic and creative enhancement templates',
        color: '#ef4444',
        icon: 'palette'
      },
      {
        id: 'ai-enhanced',
        name: 'AI Enhanced',
        description: 'Templates featuring AI-powered enhancements',
        color: '#06b6d4',
        icon: 'zap'
      }
    ];

    defaultCategories.forEach(category => {
      this.categories.set(category.id, category);
    });
  }

  /**
   * Save a new template
   */
  saveTemplate(
    name: string,
    description: string,
    actions: RecordedAction[],
    settings: Partial<BatchTemplate['settings']> = {},
    tags: string[] = [],
    categoryId?: string
  ): BatchTemplate {
    // Validate template data
    if (!name.trim()) {
      throw new Error('Template name is required');
    }

    if (actions.length === 0) {
      throw new Error('Template must contain at least one action');
    }

    // Check for duplicate names
    const existingTemplate = this.findTemplateByName(name);
    if (existingTemplate) {
      throw new Error(`Template with name "${name}" already exists`);
    }

    const template: BatchTemplate = {
      id: this.generateTemplateId(),
      name: name.trim(),
      description: description.trim(),
      actions: this.cloneActions(actions),
      created: new Date(),
      modified: new Date(),
      version: '1.0.0',
      tags: [...new Set(tags.map(tag => tag.toLowerCase().trim()).filter(Boolean))],
      author: this.getCurrentUser(),
      settings: {
        parallelProcessing: true,
        maxConcurrentJobs: 3,
        errorHandling: 'skip',
        retryAttempts: 2,
        enablePreview: true,
        qualityValidation: false,
        outputSettings: this.getDefaultExportSettings(),
        ...settings
      }
    };

    // Add category tag if specified
    if (categoryId && this.categories.has(categoryId)) {
      template.tags.push(`category:${categoryId}`);
    }

    // Generate thumbnail
    template.thumbnail = this.generateThumbnail(template);

    this.templates.set(template.id, template);
    this.saveToStorage();
    
    this.emitter.emit('templateSaved', template);
    return template;
  }

  /**
   * Load a template by ID
   */
  loadTemplate(templateId: string): BatchTemplate | null {
    const template = this.templates.get(templateId);
    if (template) {
      this.trackUsage(templateId);
      return this.cloneTemplate(template);
    }
    return null;
  }

  /**
   * Update an existing template
   */
  updateTemplate(templateId: string, updates: Partial<BatchTemplate>): boolean {
    const template = this.templates.get(templateId);
    if (!template) {
      return false;
    }

    // Validate updates
    if (updates.name && updates.name !== template.name) {
      const existingTemplate = this.findTemplateByName(updates.name);
      if (existingTemplate && existingTemplate.id !== templateId) {
        throw new Error(`Template with name "${updates.name}" already exists`);
      }
    }

    // Update version if actions changed
    let newVersion = template.version;
    if (updates.actions && JSON.stringify(updates.actions) !== JSON.stringify(template.actions)) {
      newVersion = this.incrementVersion(template.version);
    }

    const updatedTemplate: BatchTemplate = {
      ...template,
      ...updates,
      id: template.id, // Ensure ID cannot be changed
      created: template.created, // Preserve creation date
      modified: new Date(),
      version: newVersion,
      tags: updates.tags ? [...new Set(updates.tags.map(tag => tag.toLowerCase().trim()).filter(Boolean))] : template.tags
    };

    this.templates.set(templateId, updatedTemplate);
    this.saveToStorage();
    
    this.emitter.emit('templateUpdated', updatedTemplate);
    return true;
  }

  /**
   * Delete a template
   */
  deleteTemplate(templateId: string): boolean {
    const template = this.templates.get(templateId);
    if (!template) {
      return false;
    }

    this.templates.delete(templateId);
    this.templateUsage.delete(templateId);
    this.recentlyUsed = this.recentlyUsed.filter(id => id !== templateId);
    
    this.saveToStorage();
    this.emitter.emit('templateDeleted', [templateId, template]);
    
    return true;
  }

  /**
   * Get all templates
   */
  getAllTemplates(): BatchTemplate[] {
    return Array.from(this.templates.values()).map(template => this.cloneTemplate(template));
  }

  /**
   * Search templates with advanced filtering
   */
  searchTemplates(options: TemplateSearchOptions = {}): BatchTemplate[] {
    let results = this.getAllTemplates();

    // Text search
    if (options.query) {
      const query = options.query.toLowerCase();
      results = results.filter(template => 
        template.name.toLowerCase().includes(query) ||
        template.description.toLowerCase().includes(query) ||
        template.tags.some(tag => tag.includes(query)) ||
        template.author.toLowerCase().includes(query)
      );
    }

    // Tag filtering
    if (options.tags && options.tags.length > 0) {
      const searchTags = options.tags.map(tag => tag.toLowerCase());
      results = results.filter(template =>
        searchTags.some(tag => template.tags.includes(tag))
      );
    }

    // Category filtering
    if (options.categories && options.categories.length > 0) {
      const categoryTags = options.categories.map(cat => `category:${cat}`);
      results = results.filter(template =>
        categoryTags.some(catTag => template.tags.includes(catTag))
      );
    }

    // Action type filtering
    if (options.actionTypes && options.actionTypes.length > 0) {
      results = results.filter(template =>
        options.actionTypes!.some(actionType =>
          template.actions.some(action => action.type === actionType)
        )
      );
    }

    // Author filtering
    if (options.author) {
      results = results.filter(template =>
        template.author.toLowerCase().includes(options.author!.toLowerCase())
      );
    }

    // Date range filtering
    if (options.dateRange) {
      results = results.filter(template => {
        const created = template.created.getTime();
        const start = options.dateRange!.start.getTime();
        const end = options.dateRange!.end.getTime();
        return created >= start && created <= end;
      });
    }

    // Sorting
    if (options.sortBy) {
      results.sort((a, b) => {
        let comparison = 0;
        
        switch (options.sortBy) {
          case 'name':
            comparison = a.name.localeCompare(b.name);
            break;
          case 'created':
            comparison = a.created.getTime() - b.created.getTime();
            break;
          case 'modified':
            comparison = a.modified.getTime() - b.modified.getTime();
            break;
          case 'usage':
            const usageA = this.templateUsage.get(a.id) || 0;
            const usageB = this.templateUsage.get(b.id) || 0;
            comparison = usageA - usageB;
            break;
          case 'actions':
            comparison = a.actions.length - b.actions.length;
            break;
        }

        return options.sortOrder === 'desc' ? -comparison : comparison;
      });
    }

    // Limit results
    if (options.limit && options.limit > 0) {
      results = results.slice(0, options.limit);
    }

    return results;
  }

  /**
   * Duplicate a template
   */
  duplicateTemplate(templateId: string, newName?: string): BatchTemplate | null {
    const original = this.templates.get(templateId);
    if (!original) {
      return null;
    }

    const baseName = newName || `${original.name} (Copy)`;
    let duplicateName = baseName;
    let counter = 1;

    // Ensure unique name
    while (this.findTemplateByName(duplicateName)) {
      duplicateName = `${baseName} ${counter}`;
      counter++;
    }

    const duplicate: BatchTemplate = {
      ...original,
      id: this.generateTemplateId(),
      name: duplicateName,
      created: new Date(),
      modified: new Date(),
      version: '1.0.0'
    };

    this.templates.set(duplicate.id, duplicate);
    this.saveToStorage();
    
    this.emitter.emit('templateDuplicated', [duplicate, original]);
    return duplicate;
  }

  /**
   * Export template as JSON
   */
  exportTemplate(templateId: string): string {
    const template = this.templates.get(templateId);
    if (!template) {
      throw new Error('Template not found');
    }

    const exportData = {
      version: '1.0',
      exportDate: new Date().toISOString(),
      template: template
    };

    return JSON.stringify(exportData, null, 2);
  }

  /**
   * Import template from JSON
   */
  importTemplate(templateJson: string, options: { overwrite?: boolean; rename?: boolean } = {}): BatchTemplate {
    let templateData: any;
    
    try {
      templateData = JSON.parse(templateJson);
    } catch (error) {
      throw new Error('Invalid JSON format');
    }

    // Validate structure
    const template = templateData.template || templateData;
    if (!this.isValidTemplateStructure(template)) {
      throw new Error('Invalid template structure');
    }

    // Handle naming conflicts
    let importedTemplate: BatchTemplate = {
      ...template,
      id: this.generateTemplateId(),
      created: new Date(template.created),
      modified: new Date(),
      version: template.version || '1.0.0'
    };

    // Check for name conflicts
    const existingTemplate = this.findTemplateByName(importedTemplate.name);
    if (existingTemplate) {
      if (options.overwrite) {
        this.deleteTemplate(existingTemplate.id);
      } else if (options.rename !== false) {
        importedTemplate.name = this.generateUniqueName(importedTemplate.name);
      } else {
        throw new Error(`Template with name "${importedTemplate.name}" already exists`);
      }
    }

    this.templates.set(importedTemplate.id, importedTemplate);
    this.saveToStorage();
    
    this.emitter.emit('templateImported', importedTemplate);
    return importedTemplate;
  }

  /**
   * Validate template structure and actions
   */
  validateTemplate(template: BatchTemplate): TemplateValidationResult {
    const result: TemplateValidationResult = {
      isValid: true,
      errors: [],
      warnings: [],
      suggestions: []
    };

    // Basic validation
    if (!template.name || !template.name.trim()) {
      result.errors.push('Template name is required');
      result.isValid = false;
    }

    if (!template.actions || template.actions.length === 0) {
      result.errors.push('Template must contain at least one action');
      result.isValid = false;
    }

    // Action validation
    if (template.actions) {
      template.actions.forEach((action, index) => {
        if (!action.id || !action.type || !action.name) {
          result.errors.push(`Action ${index + 1} is missing required fields`);
          result.isValid = false;
        }

        // Check for deprecated action types
        if (action.type === 'deprecated_action' as ActionType) {
          result.warnings.push(`Action ${index + 1} uses deprecated action type`);
        }

        // Check for missing parameters
        if (!action.parameters || Object.keys(action.parameters).length === 0) {
          result.warnings.push(`Action ${index + 1} has no parameters`);
        }
      });
    }

    // Settings validation
    if (template.settings) {
      if (template.settings.maxConcurrentJobs < 1 || template.settings.maxConcurrentJobs > 10) {
        result.warnings.push('Max concurrent jobs should be between 1 and 10');
      }

      if (template.settings.retryAttempts > 5) {
        result.warnings.push('High retry attempts may slow down processing');
      }
    }

    // Suggestions
    if (template.actions.length > 20) {
      result.suggestions.push('Consider breaking down large templates into smaller, reusable templates');
    }

    if (!template.description || template.description.length < 10) {
      result.suggestions.push('Add a detailed description to help users understand the template purpose');
    }

    if (template.tags.length === 0) {
      result.suggestions.push('Add tags to make the template easier to find');
    }

    return result;
  }

  /**
   * Get template statistics
   */
  getTemplateStats(): TemplateStats {
    const templates = this.getAllTemplates();
    const actionTypeCounts = new Map<ActionType, number>();
    const categoryCount = new Map<string, number>();
    let totalActions = 0;

    templates.forEach(template => {
      totalActions += template.actions.length;
      
      // Count action types
      template.actions.forEach(action => {
        actionTypeCounts.set(action.type, (actionTypeCounts.get(action.type) || 0) + 1);
      });

      // Count categories
      template.tags.forEach(tag => {
        if (tag.startsWith('category:')) {
          const category = tag.replace('category:', '');
          categoryCount.set(category, (categoryCount.get(category) || 0) + 1);
        }
      });
    });

    const mostUsedActionTypes = Array.from(actionTypeCounts.entries())
      .map(([type, count]) => ({ type, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 5);

    const recentlyUsedTemplates = this.recentlyUsed
      .slice(0, 5)
      .map(id => this.templates.get(id))
      .filter(Boolean) as BatchTemplate[];

    const popularTemplates = Array.from(this.templateUsage.entries())
      .map(([id, usageCount]) => ({
        template: this.templates.get(id)!,
        usageCount
      }))
      .filter(item => item.template)
      .sort((a, b) => b.usageCount - a.usageCount)
      .slice(0, 5);

    return {
      totalTemplates: templates.length,
      totalActions,
      mostUsedActionTypes,
      averageActionsPerTemplate: templates.length > 0 ? totalActions / templates.length : 0,
      templatesByCategory: categoryCount,
      recentlyUsed: recentlyUsedTemplates,
      popularTemplates
    };
  }

  /**
   * Get template categories
   */
  getCategories(): TemplateCategory[] {
    return Array.from(this.categories.values());
  }

  /**
   * Add or update a category
   */
  saveCategory(category: TemplateCategory): void {
    this.categories.set(category.id, { ...category });
    this.saveToStorage();
    this.emitter.emit('categorySaved', category);
  }

  /**
   * Delete a category
   */
  deleteCategory(categoryId: string): boolean {
    if (!this.categories.has(categoryId)) {
      return false;
    }

    // Remove category from all templates
    const categoryTag = `category:${categoryId}`;
    this.templates.forEach(template => {
      template.tags = template.tags.filter(tag => tag !== categoryTag);
    });

    this.categories.delete(categoryId);
    this.saveToStorage();
    this.emitter.emit('categoryDeleted', categoryId);
    
    return true;
  }

  // Private helper methods
  private generateTemplateId(): string {
    return `template_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private getCurrentUser(): string {
    // In a real app, this would get the current user from auth context
    return 'user';
  }

  private getDefaultExportSettings(): ExportSettings {
    return {
      format: 'jpeg',
      quality: 85,
      compression: 0.8,
      preserveMetadata: false,
      filenamePattern: '{originalName}_processed',
      folder: 'batch_output'
    };
  }

  private generateThumbnail(template: BatchTemplate): string {
    // Generate a simple thumbnail representation
    // In a real implementation, this might create an actual image thumbnail
    const actionTypes = [...new Set(template.actions.map(a => a.type))];
    return `data:image/svg+xml,${encodeURIComponent(`
      <svg width="64" height="64" xmlns="http://www.w3.org/2000/svg">
        <rect width="64" height="64" fill="#f3f4f6"/>
        <text x="32" y="20" text-anchor="middle" font-size="10" fill="#374151">
          ${template.name.substring(0, 8)}
        </text>
        <text x="32" y="40" text-anchor="middle" font-size="8" fill="#6b7280">
          ${template.actions.length} actions
        </text>
        <text x="32" y="55" text-anchor="middle" font-size="6" fill="#9ca3af">
          ${actionTypes.length} types
        </text>
      </svg>
    `)}`;
  }

  private cloneTemplate(template: BatchTemplate): BatchTemplate {
    return JSON.parse(JSON.stringify(template));
  }

  private cloneActions(actions: RecordedAction[]): RecordedAction[] {
    return JSON.parse(JSON.stringify(actions));
  }

  private findTemplateByName(name: string): BatchTemplate | null {
    for (const template of this.templates.values()) {
      if (template.name === name) {
        return template;
      }
    }
    return null;
  }

  private generateUniqueName(baseName: string): string {
    let name = baseName;
    let counter = 1;
    
    while (this.findTemplateByName(name)) {
      name = `${baseName} (${counter})`;
      counter++;
    }
    
    return name;
  }

  private incrementVersion(version: string): string {
    const parts = version.split('.');
    const patch = parseInt(parts[2] || '0') + 1;
    return `${parts[0] || '1'}.${parts[1] || '0'}.${patch}`;
  }

  private trackUsage(templateId: string): void {
    this.templateUsage.set(templateId, (this.templateUsage.get(templateId) || 0) + 1);
    
    // Update recently used list
    this.recentlyUsed = this.recentlyUsed.filter(id => id !== templateId);
    this.recentlyUsed.unshift(templateId);
    this.recentlyUsed = this.recentlyUsed.slice(0, 10); // Keep only 10 recent items
    
    this.saveToStorage();
  }

  private isValidTemplateStructure(template: any): boolean {
    return (
      template &&
      typeof template.name === 'string' &&
      Array.isArray(template.actions) &&
      template.actions.length > 0 &&
      template.actions.every((action: any) => 
        action.id && action.type && action.name && action.parameters
      )
    );
  }

  private loadFromStorage(): void {
    try {
      // Load templates
      const templatesData = this.localStorage.getItem(this.STORAGE_KEY);
      if (templatesData) {
        const templates: BatchTemplate[] = JSON.parse(templatesData);
        templates.forEach(template => {
          // Convert date strings back to Date objects
          template.created = new Date(template.created);
          template.modified = new Date(template.modified);
          this.templates.set(template.id, template);
        });
      }

      // Load categories
      const categoriesData = this.localStorage.getItem(this.CATEGORIES_KEY);
      if (categoriesData) {
        const categories: TemplateCategory[] = JSON.parse(categoriesData);
        categories.forEach(category => {
          this.categories.set(category.id, category);
        });
      }

      // Load usage data
      const usageData = this.localStorage.getItem(this.USAGE_KEY);
      if (usageData) {
        const usage = JSON.parse(usageData);
        this.templateUsage = new Map(usage.templateUsage || []);
        this.recentlyUsed = usage.recentlyUsed || [];
      }

    } catch (error) {
      console.error('Failed to load templates from storage:', error);
    }
  }

  private saveToStorage(): void {
    try {
      // Save templates
      const templates = Array.from(this.templates.values());
      this.localStorage.setItem(this.STORAGE_KEY, JSON.stringify(templates));

      // Save categories
      const categories = Array.from(this.categories.values());
      this.localStorage.setItem(this.CATEGORIES_KEY, JSON.stringify(categories));

      // Save usage data
      const usageData = {
        templateUsage: Array.from(this.templateUsage.entries()),
        recentlyUsed: this.recentlyUsed
      };
      this.localStorage.setItem(this.USAGE_KEY, JSON.stringify(usageData));

    } catch (error) {
      console.error('Failed to save templates to storage:', error);
    }
  }

  /**
   * Clear all data (useful for testing or reset)
   */
  clearAll(): void {
    this.templates.clear();
    this.templateUsage.clear();
    this.recentlyUsed = [];
    
    this.localStorage.removeItem(this.STORAGE_KEY);
    this.localStorage.removeItem(this.USAGE_KEY);
    
    this.emitter.emit('allTemplatesCleared');
  }

  /**
   * Get memory usage information
   */
  getMemoryUsage(): {
    templateCount: number;
    totalActions: number;
    estimatedSizeKB: number;
  } {
    const templates = this.getAllTemplates();
    const totalActions = templates.reduce((sum, template) => sum + template.actions.length, 0);
    
    // Rough estimation of memory usage
    const templatesJson = JSON.stringify(templates);
    const estimatedSizeKB = Math.round(templatesJson.length / 1024);
    
    return {
      templateCount: templates.length,
      totalActions,
      estimatedSizeKB
    };
  }

  /**
   * Clean up resources
   */
  destroy(): void {
    // Cleanup if necessary. With mitt, this is often unnecessary as mitt instances
    // typically don't require explicit cleanup unless functionality is added.
    // Otherwise, if anything needs detaching or clearing, do it here.
  }
}
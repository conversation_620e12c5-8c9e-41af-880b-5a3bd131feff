import { useEffect, useRef, useState } from "react";
import { useLocation } from "react-router-dom";
import AlertDropdown from "@/alerts/alertDropDown";
import DataStaxLogo from "@/assets/DataStaxLogo.svg?react";
import <PERSON><PERSON><PERSON>ogo from "@/assets/LangflowLogo.svg?react";
import ForwardedIconComponent from "@/components/common/genericIconComponent";
import ShadTooltip from "@/components/common/shadTooltipComponent";
import ModelSelector from "@/components/core/modelSelector";
import WorkspaceSelector from "@/components/core/workspaceSelector";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { SidebarTrigger } from "@/components/ui/sidebar";
import CustomAccountMenu from "@/customization/components/custom-AccountMenu";
import CustomLangflowCounts from "@/customization/components/custom-langflow-counts";
import { CustomOrgSelector } from "@/customization/components/custom-org-selector";
import { CustomProductSelector } from "@/customization/components/custom-product-selector";
import { ENABLE_DATASTAX_LANGFLOW } from "@/customization/feature-flags";
import { useCustomNavigate } from "@/customization/hooks/use-custom-navigate";
import useTheme from "@/customization/hooks/use-custom-theme";
import useAlertStore from "@/stores/alertStore";
import FlowMenu from "./components/FlowMenu";

export default function AppHeader(): JSX.Element {
  const notificationCenter = useAlertStore((state) => state.notificationCenter);
  const notificationList = useAlertStore((state) => state.notificationList);
  const navigate = useCustomNavigate();
  const location = useLocation();
  const [activeState, setActiveState] = useState<"notifications" | null>(null);
  const notificationRef = useRef<HTMLButtonElement | null>(null);
  const notificationContentRef = useRef<HTMLDivElement | null>(null);

  // Chat state for header controls
  const [isPrivate, setIsPrivate] = useState(true);

  useTheme();

  // Determine which navigation icons to show based on current route
  const isOnFlowsPages =
    location.pathname.startsWith("/flows") ||
    location.pathname.startsWith("/components") ||
    location.pathname.startsWith("/all") ||
    location.pathname.startsWith("/mcp") ||
    location.pathname.startsWith("/flow/");
  const isOnChatPage = location.pathname.startsWith("/chat");
  const isOnCRMPage = location.pathname.startsWith("/crm");
  const isOnAnalyticsPage = location.pathname.startsWith("/analytics");
  const isOnPhotoEditorPage = location.pathname.startsWith("/photo-editor");
  const isOnBookEditorPage = location.pathname.startsWith("/book-editor");
  const isOnVideoEditorPage = location.pathname.startsWith("/video-editor");
  const isOnTaskManagementPage =
    location.pathname.startsWith("/task-management");
  const isOnIDEPage = location.pathname.startsWith("/ide");
  const isOnOtherPages =
    !isOnFlowsPages &&
    !isOnChatPage &&
    !isOnCRMPage &&
    !isOnAnalyticsPage &&
    !isOnPhotoEditorPage &&
    !isOnBookEditorPage &&
    !isOnVideoEditorPage &&
    !isOnTaskManagementPage &&
    !isOnIDEPage;

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      const target = event.target as Node;
      const isNotificationButton = notificationRef.current?.contains(target);
      const isNotificationContent =
        notificationContentRef.current?.contains(target);

      if (!isNotificationButton && !isNotificationContent) {
        setActiveState(null);
      }
    }

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const getNotificationBadge = () => {
    const baseClasses = "absolute h-1 w-1 rounded-full bg-destructive";
    return notificationCenter && notificationList.length > 0
      ? `${baseClasses} right-[0.3rem] top-[5px]`
      : "hidden";
  };

  return (
    <div
      className={`z-10 flex h-[48px] w-full items-center justify-between border-b px-6 dark:bg-background`}
      data-testid="app-header"
    >
      {/* Left Section */}
      <div
        className={`z-30 flex shrink-0 items-center gap-2`}
        data-testid="header_left_section_wrapper"
      >
        <Button
          unstyled
          onClick={() => navigate("/")}
          className="mr-1 flex h-8 w-8 items-center"
          data-testid="icon-ChevronLeft"
        >
          {ENABLE_DATASTAX_LANGFLOW ? (
            <DataStaxLogo className="fill-black dark:fill-[white]" />
          ) : (
            <LangflowLogo className="h-6 w-6" />
          )}
        </Button>

        {/* Chat Sidebar Toggle - Only on Chat Page */}
        {isOnChatPage && (
          <ShadTooltip content="Toggle sidebar" side="bottom">
            <Button
              unstyled
              onClick={() => {
                // Trigger the actual SidebarTrigger button in the chat page
                const sidebarTrigger = document.querySelector(
                  '[data-sidebar="trigger"]',
                );
                if (sidebarTrigger) {
                  (sidebarTrigger as HTMLButtonElement).click();
                }
              }}
              className="mr-1 flex h-8 w-8 items-center hover:bg-accent rounded-md"
              data-testid="chat-sidebar-toggle"
            >
              <ForwardedIconComponent name="PanelLeft" className="h-4 w-4" />
            </Button>
          </ShadTooltip>
        )}

        {/* CRM Sidebar Toggle - Only on CRM Page */}
        {isOnCRMPage && (
          <ShadTooltip content="Toggle sidebar" side="bottom">
            <Button
              unstyled
              onClick={() => {
                // Trigger the actual SidebarTrigger button in the CRM page
                const sidebarTrigger = document.querySelector(
                  '[data-sidebar="trigger"]',
                );
                if (sidebarTrigger) {
                  (sidebarTrigger as HTMLButtonElement).click();
                }
              }}
              className="mr-1 flex h-8 w-8 items-center hover:bg-accent rounded-md"
              data-testid="crm-sidebar-toggle"
            >
              <ForwardedIconComponent name="PanelLeft" className="h-4 w-4" />
            </Button>
          </ShadTooltip>
        )}
        {ENABLE_DATASTAX_LANGFLOW && (
          <>
            <CustomOrgSelector />
            <CustomProductSelector />
          </>
        )}

        {/* Workspace Selector - Always visible */}
        <WorkspaceSelector
          onCreateWorkspace={() => {
            console.log("Workspace created successfully");
          }}
          onWorkspaceSettings={(workspace) => {
            console.log("Navigate to workspace settings for:", workspace.name);
          }}
          onManageMembers={(workspace) => {
            console.log("Navigate to member management for:", workspace.name);
          }}
        />
      </div>

      {/* Middle Section */}
      <div className="absolute left-1/2 -translate-x-1/2">
        {isOnChatPage ? (
          <div className="flex items-center gap-3">
            {/* Chat Model Selector */}
            <ModelSelector
              onAddApiKey={() => navigate("/settings/global-variables")}
              className="w-[160px] h-8"
            />

            {/* Privacy Selector */}
            <Select
              value={isPrivate ? "private" : "public"}
              onValueChange={(value) => setIsPrivate(value === "private")}
            >
              <SelectTrigger className="w-[100px] h-8">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="private">
                  <div className="flex items-center gap-2">
                    <ForwardedIconComponent name="Lock" className="h-3 w-3" />
                    Private
                  </div>
                </SelectItem>
                <SelectItem value="public">
                  <div className="flex items-center gap-2">
                    <ForwardedIconComponent name="Globe" className="h-3 w-3" />
                    Public
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
        ) : (
          <FlowMenu />
        )}
      </div>

      {/* Right Section */}
      <div
        className={`relative left-3 z-30 flex shrink-0 items-center gap-3`}
        data-testid="header_right_section_wrapper"
      >
        <>
          <Button
            unstyled
            className="hidden items-center whitespace-nowrap pr-2 lg:inline"
          >
            <CustomLangflowCounts />
          </Button>
        </>

        {/* Navigation Icons */}
        <div className="flex items-center gap-2">
          {/* Show Flows icon when on Chat page, CRM page, Analytics page, Photo Editor page, Video Editor page, Task Management page, IDE page, or other pages */}
          {(isOnChatPage ||
            isOnCRMPage ||
            isOnAnalyticsPage ||
            isOnPhotoEditorPage ||
            isOnVideoEditorPage ||
            isOnTaskManagementPage ||
            isOnIDEPage ||
            isOnOtherPages) && (
            <ShadTooltip content="Flows" side="bottom">
              <Button
                unstyled
                onClick={() => navigate("/flows")}
                className="hit-area-hover group relative items-center rounded-md px-2 py-2 text-muted-foreground"
                data-testid="flows-nav-button"
              >
                <ForwardedIconComponent
                  name="Workflow"
                  className="h-4 w-4 text-muted-foreground group-hover:text-primary"
                  strokeWidth={2}
                />
              </Button>
            </ShadTooltip>
          )}

          {/* Show Chat icon when on Flows pages, CRM page, Analytics page, Photo Editor page, Video Editor page, Task Management page, IDE page, or other pages */}
          {(isOnFlowsPages ||
            isOnCRMPage ||
            isOnAnalyticsPage ||
            isOnPhotoEditorPage ||
            isOnVideoEditorPage ||
            isOnTaskManagementPage ||
            isOnIDEPage ||
            isOnOtherPages) && (
            <ShadTooltip content="Chat" side="bottom">
              <Button
                unstyled
                onClick={() => navigate("/chat")}
                className="hit-area-hover group relative items-center rounded-md px-2 py-2 text-muted-foreground"
                data-testid="chat-nav-button"
              >
                <ForwardedIconComponent
                  name="MessageSquare"
                  className="h-4 w-4 text-muted-foreground group-hover:text-primary"
                  strokeWidth={2}
                />
              </Button>
            </ShadTooltip>
          )}

          {/* Show CRM icon when not on CRM page */}
          {!isOnCRMPage && (
            <ShadTooltip content="CRM" side="bottom">
              <Button
                unstyled
                onClick={() => navigate("/crm")}
                className="hit-area-hover group relative items-center rounded-md px-2 py-2 text-muted-foreground"
                data-testid="crm-nav-button"
              >
                <ForwardedIconComponent
                  name="Users"
                  className="h-4 w-4 text-muted-foreground group-hover:text-primary"
                  strokeWidth={2}
                />
              </Button>
            </ShadTooltip>
          )}

          {/* Show Analytics icon when not on Analytics page */}
          {!isOnAnalyticsPage && (
            <ShadTooltip content="Analytics" side="bottom">
              <Button
                unstyled
                onClick={() => navigate("/analytics")}
                className="hit-area-hover group relative items-center rounded-md px-2 py-2 text-muted-foreground"
                data-testid="analytics-nav-button"
              >
                <ForwardedIconComponent
                  name="BarChart3"
                  className="h-4 w-4 text-muted-foreground group-hover:text-primary"
                  strokeWidth={2}
                />
              </Button>
            </ShadTooltip>
          )}

          {/* Show Photo Editor icon when on Flows pages, Chat page, CRM page, Book Editor page, Video Editor page, Task Management page, IDE page, or other pages */}
          {(isOnFlowsPages ||
            isOnChatPage ||
            isOnCRMPage ||
            isOnBookEditorPage ||
            isOnVideoEditorPage ||
            isOnTaskManagementPage ||
            isOnIDEPage ||
            isOnOtherPages) && (
            <ShadTooltip content="Photo Editor" side="bottom">
              <Button
                unstyled
                onClick={() => navigate("/photo-editor")}
                className="hit-area-hover group relative items-center rounded-md px-2 py-2 text-muted-foreground"
                data-testid="photo-editor-nav-button"
              >
                <ForwardedIconComponent
                  name="ImageIcon"
                  className="h-4 w-4 text-muted-foreground group-hover:text-primary"
                  strokeWidth={2}
                />
              </Button>
            </ShadTooltip>
          )}

          {/* Show Video Editor icon when on Flows pages, Chat page, CRM page, Photo Editor page, Book Editor page, Task Management page, IDE page, or other pages */}
          {(isOnFlowsPages ||
            isOnChatPage ||
            isOnCRMPage ||
            isOnPhotoEditorPage ||
            isOnBookEditorPage ||
            isOnTaskManagementPage ||
            isOnIDEPage ||
            isOnOtherPages) && (
            <ShadTooltip content="Video Editor" side="bottom">
              <Button
                unstyled
                onClick={() => navigate("/video-editor")}
                className="hit-area-hover group relative items-center rounded-md px-2 py-2 text-muted-foreground"
                data-testid="video-editor-nav-button"
              >
                <ForwardedIconComponent
                  name="Video"
                  className="h-4 w-4 text-muted-foreground group-hover:text-primary"
                  strokeWidth={2}
                />
              </Button>
            </ShadTooltip>
          )}

          {/* Show Book Editor icon when on Flows pages, Chat page, CRM page, Photo Editor page, Video Editor page, Task Management page, IDE page, or other pages */}
          {(isOnFlowsPages ||
            isOnChatPage ||
            isOnCRMPage ||
            isOnPhotoEditorPage ||
            isOnVideoEditorPage ||
            isOnTaskManagementPage ||
            isOnIDEPage ||
            isOnOtherPages) && (
            <ShadTooltip content="Book Editor" side="bottom">
              <Button
                unstyled
                onClick={() => navigate("/book-editor")}
                className="hit-area-hover group relative items-center rounded-md px-2 py-2 text-muted-foreground"
                data-testid="book-editor-nav-button"
              >
                <ForwardedIconComponent
                  name="BookOpen"
                  className="h-4 w-4 text-muted-foreground group-hover:text-primary"
                  strokeWidth={2}
                />
              </Button>
            </ShadTooltip>
          )}

          {/* Show Task Management icon when not on Task Management page */}
          {!isOnTaskManagementPage && (
            <ShadTooltip content="Task Management" side="bottom">
              <Button
                unstyled
                onClick={() => navigate("/task-management")}
                className="hit-area-hover group relative items-center rounded-md px-2 py-2 text-muted-foreground"
                data-testid="task-management-nav-button"
              >
                <ForwardedIconComponent
                  name="KanbanSquare"
                  className="h-4 w-4 text-muted-foreground group-hover:text-primary"
                  strokeWidth={2}
                />
              </Button>
            </ShadTooltip>
          )}

          {/* Show IDE icon when not on IDE page */}
          {!isOnIDEPage && (
            <ShadTooltip content="IDE" side="bottom">
              <Button
                unstyled
                onClick={() => navigate("/ide")}
                className="hit-area-hover group relative items-center rounded-md px-2 py-2 text-muted-foreground"
                data-testid="ide-nav-button"
              >
                <ForwardedIconComponent
                  name="Code2"
                  className="h-4 w-4 text-muted-foreground group-hover:text-primary"
                  strokeWidth={2}
                />
              </Button>
            </ShadTooltip>
          )}
        </div>

        <AlertDropdown
          notificationRef={notificationContentRef}
          onClose={() => setActiveState(null)}
        >
          <ShadTooltip
            content="Notifications and errors"
            side="bottom"
            styleClasses="z-10"
          >
            <AlertDropdown onClose={() => setActiveState(null)}>
              <Button
                ref={notificationRef}
                unstyled
                onClick={() =>
                  setActiveState((prev) =>
                    prev === "notifications" ? null : "notifications",
                  )
                }
                data-testid="notification_button"
              >
                <div className="hit-area-hover group relative items-center rounded-md px-2 py-2 text-muted-foreground">
                  <span className={getNotificationBadge()} />
                  <ForwardedIconComponent
                    name="Bell"
                    className={`side-bar-button-size h-4 w-4 ${
                      activeState === "notifications"
                        ? "text-primary"
                        : "text-muted-foreground group-hover:text-primary"
                    }`}
                    strokeWidth={2}
                  />
                  <span className="hidden whitespace-nowrap">
                    Notifications
                  </span>
                </div>
              </Button>
            </AlertDropdown>
          </ShadTooltip>
        </AlertDropdown>
        <Separator
          orientation="vertical"
          className="my-auto h-7 dark:border-zinc-700"
        />

        <div className="flex">
          <CustomAccountMenu />
        </div>
      </div>
    </div>
  );
}

import React, { useMemo } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useWorkspaceCustomization } from '@/hooks/useWorkspaceCustomization';
import { cn } from '@/lib/utils';
import {
  Home,
  Users,
  FileText,
  BarChart3,
  Settings,
  Camera,
  Video,
  Code,
  Bot,
  ChevronDown,
  ChevronRight,
  Lock,
  BookOpen,
} from 'lucide-react';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

interface NavigationItem {
  id: string;
  label: string;
  icon: React.ReactNode;
  path?: string;
  feature?: string;
  badge?: string;
  children?: NavigationItem[];
}

const iconMap: Record<string, React.ReactNode> = {
  home: <Home className="h-4 w-4" />,
  users: <Users className="h-4 w-4" />,
  fileText: <FileText className="h-4 w-4" />,
  barChart: <BarChart3 className="h-4 w-4" />,
  settings: <Settings className="h-4 w-4" />,
  camera: <Camera className="h-4 w-4" />,
  video: <Video className="h-4 w-4" />,
  code: <Code className="h-4 w-4" />,
  bot: <Bot className="h-4 w-4" />,
  bookOpen: <BookOpen className="h-4 w-4" />,
};

const defaultNavigation: NavigationItem[] = [
  {
    id: 'dashboard',
    label: 'Dashboard',
    icon: iconMap.home,
    path: '/',
  },
  {
    id: 'crm',
    label: 'CRM',
    icon: iconMap.users,
    feature: 'crm',
    children: [
      {
        id: 'customers',
        label: 'Customers',
        icon: iconMap.users,
        path: '/crm/customers',
        feature: 'crm.customers',
      },
      {
        id: 'notes',
        label: 'Notes',
        icon: iconMap.fileText,
        path: '/crm/notes',
        feature: 'crm.notes',
      },
      {
        id: 'analytics',
        label: 'Analytics',
        icon: iconMap.barChart,
        path: '/crm/analytics',
        feature: 'crm.analytics',
        badge: 'Pro',
      },
    ],
  },
  {
    id: 'photo-editor',
    label: 'Photo Editor',
    icon: iconMap.camera,
    path: '/photo-editor',
    feature: 'photoEditor',
  },
  {
    id: 'video-editor',
    label: 'Video Editor',
    icon: iconMap.video,
    path: '/video-editor',
    feature: 'videoEditor',
    badge: 'Beta',
  },
  {
    id: 'book-editor',
    label: 'Book Editor',
    icon: iconMap.bookOpen,
    path: '/book-editor',
    feature: 'bookEditor',
  },
  {
    id: 'ide',
    label: 'IDE',
    icon: iconMap.code,
    path: '/ide',
    feature: 'ide',
  },
  {
    id: 'agents',
    label: 'AI Agents',
    icon: iconMap.bot,
    path: '/agents',
    feature: 'agents',
  },
  {
    id: 'settings',
    label: 'Settings',
    icon: iconMap.settings,
    path: '/settings',
  },
];

interface DynamicNavigationProps {
  collapsed?: boolean;
  className?: string;
}

export const DynamicNavigation: React.FC<DynamicNavigationProps> = ({
  collapsed = false,
  className,
}) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { customization, isFeatureEnabled } = useWorkspaceCustomization();

  // Build navigation based on features and custom items
  const navigation = useMemo(() => {
    const baseNav = [...defaultNavigation];
    
    // Add custom navigation items
    if (customization.navigation) {
      customization.navigation.forEach((customItem) => {
        const navItem: NavigationItem = {
          id: customItem.id,
          label: customItem.label,
          icon: iconMap[customItem.icon || 'fileText'] || iconMap.fileText,
          path: customItem.path,
          children: customItem.children?.map(child => ({
            id: child.id,
            label: child.label,
            icon: iconMap.fileText,
            path: child.path,
          })),
        };
        
        if (customItem.position !== undefined) {
          baseNav.splice(customItem.position, 0, navItem);
        } else {
          baseNav.push(navItem);
        }
      });
    }
    
    // Filter out disabled features
    return filterNavigationByFeatures(baseNav);
  }, [customization.navigation, isFeatureEnabled]);

  const filterNavigationByFeatures = (items: NavigationItem[]): NavigationItem[] => {
    return items.filter(item => {
      // Always show items without feature requirements
      if (!item.feature) return true;
      
      // Check if feature is enabled
      if (!isFeatureEnabled(item.feature)) return false;
      
      // Filter children recursively
      if (item.children) {
        item.children = filterNavigationByFeatures(item.children);
      }
      
      return true;
    });
  };

  const isActive = (path?: string): boolean => {
    if (!path) return false;
    return location.pathname === path || location.pathname.startsWith(path + '/');
  };

  const renderNavigationItem = (item: NavigationItem, depth = 0) => {
    const hasChildren = item.children && item.children.length > 0;
    const active = isActive(item.path);

    if (hasChildren) {
      return (
        <Collapsible key={item.id} defaultOpen={item.children.some(child => isActive(child.path))}>
          <CollapsibleTrigger asChild>
            <Button
              variant="ghost"
              className={cn(
                "w-full justify-start",
                depth > 0 && "pl-8",
                active && "bg-accent"
              )}
            >
              {item.icon}
              {!collapsed && (
                <>
                  <span className="ml-2 flex-1 text-left">{item.label}</span>
                  <ChevronDown className="h-4 w-4 transition-transform data-[state=open]:rotate-180" />
                </>
              )}
            </Button>
          </CollapsibleTrigger>
          {!collapsed && (
            <CollapsibleContent>
              <div className="ml-4 space-y-1">
                {item.children.map(child => renderNavigationItem(child, depth + 1))}
              </div>
            </CollapsibleContent>
          )}
        </Collapsible>
      );
    }

    const button = (
      <Button
        key={item.id}
        variant="ghost"
        className={cn(
          "w-full justify-start",
          depth > 0 && "pl-8",
          active && "bg-accent"
        )}
        onClick={() => item.path && navigate(item.path)}
      >
        {item.icon}
        {!collapsed && (
          <>
            <span className="ml-2 flex-1 text-left">{item.label}</span>
            {item.badge && (
              <Badge variant="secondary" className="ml-auto">
                {item.badge}
              </Badge>
            )}
          </>
        )}
      </Button>
    );

    if (collapsed && item.label) {
      return (
        <TooltipProvider key={item.id}>
          <Tooltip>
            <TooltipTrigger asChild>{button}</TooltipTrigger>
            <TooltipContent side="right">
              <p>{item.label}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      );
    }

    return button;
  };

  return (
    <nav className={cn("space-y-1", className)}>
      {navigation.map(item => renderNavigationItem(item))}
    </nav>
  );
};

// Breadcrumb navigation component
export const DynamicBreadcrumbs: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { customization } = useWorkspaceCustomization();

  const breadcrumbs = useMemo(() => {
    const paths = location.pathname.split('/').filter(Boolean);
    const crumbs: Array<{ label: string; path: string }> = [
      { label: 'Home', path: '/' },
    ];

    let currentPath = '';
    paths.forEach((segment) => {
      currentPath += `/${segment}`;
      const label = segment
        .split('-')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ');
      crumbs.push({ label, path: currentPath });
    });

    return crumbs;
  }, [location.pathname]);

  if (!customization.layout.navigation.showBreadcrumbs) {
    return null;
  }

  return (
    <nav className="flex items-center space-x-2 text-sm">
      {breadcrumbs.map((crumb, index) => (
        <React.Fragment key={crumb.path}>
          {index > 0 && <ChevronRight className="h-4 w-4 text-muted-foreground" />}
          <button
            onClick={() => navigate(crumb.path)}
            className={cn(
              "hover:text-foreground transition-colors",
              index === breadcrumbs.length - 1
                ? "text-foreground font-medium"
                : "text-muted-foreground"
            )}
          >
            {crumb.label}
          </button>
        </React.Fragment>
      ))}
    </nav>
  );
};
/**
 * Task Kanban Board - LEVER compliant
 * Reuses existing Langflow drag-drop and UI components
 */

import {
  closestCenter,
  DndContext,
  DragEndEvent,
  DragOverlay,
  DragStartEvent,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
} from "@dnd-kit/core";
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { Loader2 } from "lucide-react";
import { useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useTaskManagementStore } from "@/stores/taskManagementStore";
import type { Task } from "@/types/task-management";
import { TaskCard } from "./TaskCard";
import { TaskColumn } from "./TaskColumn";
import { TaskDetailsPanel } from "./TaskDetailsPanel";
import "./task-management.css";

export function TaskKanbanBoard() {
  const { boardId } = useParams<{ boardId: string }>();
  const [newTaskTitle, setNewTaskTitle] = useState("");
  const [selectedTask, setSelectedTask] = useState<Task | null>(null);
  const [activeId, setActiveId] = useState<string | null>(null);
  const [draggedTask, setDraggedTask] = useState<Task | null>(null);

  const {
    currentBoard,
    loading,
    error,
    fetchBoard,
    createTask,
    moveTask,
    deleteTask,
  } = useTaskManagementStore();

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    }),
  );

  useEffect(() => {
    if (boardId) {
      fetchBoard(boardId);
    }
  }, [boardId, fetchBoard]);

  const handleDragStart = (event: DragStartEvent) => {
    const { active } = event;
    setActiveId(active.id as string);

    // Find the dragged task
    if (currentBoard) {
      for (const column of currentBoard.columns || []) {
        const task = column.tasks.find((t) => t.id === active.id);
        if (task) {
          setDraggedTask(task);
          break;
        }
      }
    }
  };

  const handleDragEnd = async (event: DragEndEvent) => {
    const { active, over } = event;

    setActiveId(null);
    setDraggedTask(null);

    if (!over || !currentBoard) return;

    const activeTaskId = active.id as string;
    const overColumnId = over.id as string;

    // Find the task and its current column
    let task = null;
    let sourceColumn = null;

    for (const column of currentBoard.columns || []) {
      const foundTask = column.tasks.find((t) => t.id === activeTaskId);
      if (foundTask) {
        task = foundTask;
        sourceColumn = column;
        break;
      }
    }

    if (!task || !sourceColumn) return;

    // If dropped on same column, just reorder
    if (sourceColumn.id === overColumnId) {
      const oldIndex = sourceColumn.tasks.indexOf(task);
      const newIndex = 0; // For simplicity, add to top

      if (oldIndex !== newIndex) {
        await moveTask(activeTaskId, {
          column_id: overColumnId,
          position: newIndex,
        });
      }
    } else {
      // Move to different column
      await moveTask(activeTaskId, {
        column_id: overColumnId,
        position: 0, // Add to top of new column
      });
    }
  };

  const handleCreateTask = async () => {
    if (!newTaskTitle.trim() || !boardId) {
      console.log("Cannot create task:", { newTaskTitle, boardId });
      return;
    }

    console.log("Creating task:", { boardId, title: newTaskTitle });
    try {
      await createTask(boardId, newTaskTitle);
      setNewTaskTitle("");
    } catch (error) {
      console.error("Failed to create task:", error);
    }
  };

  const handleTaskClick = (task: Task) => {
    setSelectedTask(task);
  };

  const handleCloseDetails = () => {
    setSelectedTask(null);
  };

  const handleDeleteTask = async (taskId: string) => {
    await deleteTask(taskId);
    setSelectedTask(null);
  };

  if (loading && !currentBoard) {
    return (
      <div className="flex h-full items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex h-full items-center justify-center">
        <p className="text-red-500">Error: {error}</p>
      </div>
    );
  }

  if (!currentBoard) {
    return (
      <div className="flex h-full items-center justify-center">
        <p>Board not found</p>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-screen bg-background overflow-hidden">
      <div className="border-b p-4">
        <h1 className="text-2xl font-bold">{currentBoard.name}</h1>
        {currentBoard.description && (
          <p className="text-gray-600">{currentBoard.description}</p>
        )}

        <div className="mt-4 flex gap-2">
          <Input
            placeholder="New task title..."
            value={newTaskTitle}
            onChange={(e) => setNewTaskTitle(e.target.value)}
            onKeyPress={(e) => e.key === "Enter" && handleCreateTask()}
            className="max-w-xs"
          />
          <Button onClick={handleCreateTask} disabled={!newTaskTitle.trim()}>
            Add Task
          </Button>
        </div>
      </div>

      <div className="flex-1 overflow-x-auto overflow-y-hidden">
        <DndContext
          sensors={sensors}
          collisionDetection={closestCenter}
          onDragStart={handleDragStart}
          onDragEnd={handleDragEnd}
        >
          <div className="flex h-full gap-4 p-4">
            {currentBoard.columns &&
              Array.isArray(currentBoard.columns) &&
              currentBoard.columns.map((column) => (
                <TaskColumn
                  key={column.id}
                  column={column}
                  onTaskClick={handleTaskClick}
                />
              ))}

            {(!currentBoard.columns ||
              !Array.isArray(currentBoard.columns) ||
              currentBoard.columns.length === 0) && (
              <div className="text-center text-gray-500 w-full">
                <p>No columns configured for this board.</p>
              </div>
            )}
          </div>

          <DragOverlay
            dropAnimation={{
              duration: 250,
              easing: "cubic-bezier(0.18, 0.67, 0.6, 1.22)",
            }}
          >
            {draggedTask ? (
              <div className="rotate-3 scale-105 shadow-2xl">
                <TaskCard task={draggedTask} />
              </div>
            ) : null}
          </DragOverlay>
        </DndContext>
      </div>

      {selectedTask && (
        <TaskDetailsPanel
          task={selectedTask}
          onClose={handleCloseDetails}
          onEdit={() => {
            // TODO: Implement edit
            console.log("Edit task", selectedTask);
          }}
          onDelete={() => handleDeleteTask(selectedTask.id)}
        />
      )}
    </div>
  );
}

export default TaskKanbanBoard;

/**
 * Task Details Panel - Simple version for demo
 */

import { X } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Task } from "@/types/task-management";

interface TaskDetailsPanelProps {
  task: Task;
  onClose: () => void;
  onEdit: () => void;
  onDelete: () => void;
}

export function TaskDetailsPanel({
  task,
  onClose,
  onEdit,
  onDelete,
}: TaskDetailsPanelProps) {
  const getPriorityBadge = () => {
    const colors = {
      low: "bg-gray-100 text-gray-800",
      medium: "bg-yellow-100 text-yellow-800",
      high: "bg-red-100 text-red-800"
    };
    
    return (
      <Badge className={`text-xs ${colors[task.priority]}`}>
        {task.priority} priority
      </Badge>
    );
  };

  return (
    <div className="fixed right-0 top-0 h-full w-1/3 bg-background border-l shadow-lg z-50">
      <div className="flex items-center justify-between p-4 border-b">
        <h2 className="text-xl font-semibold">Task Details</h2>
        <Button variant="ghost" size="icon" onClick={onClose}>
          <X className="h-4 w-4" />
        </Button>
      </div>

      <div className="p-4">
        <Card>
          <CardHeader>
            <div className="flex items-start justify-between">
              <CardTitle>{task.title}</CardTitle>
              {getPriorityBadge()}
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="text-sm font-medium">Description</label>
              <p className="text-sm text-muted-foreground mt-1">
                {task.description || "No description provided"}
              </p>
            </div>

            <div>
              <label className="text-sm font-medium">Status</label>
              <p className="text-sm text-muted-foreground mt-1">
                {task.status}
              </p>
            </div>

            <div>
              <label className="text-sm font-medium">Created</label>
              <p className="text-sm text-muted-foreground mt-1">
                {new Date(task.created_at).toLocaleString()}
              </p>
            </div>

            {task.flow_id && (
              <div>
                <label className="text-sm font-medium">Linked Flow</label>
                <p className="text-sm text-muted-foreground mt-1">
                  Flow ID: {task.flow_id}
                </p>
              </div>
            )}

            <div className="flex gap-2 pt-4">
              <Button onClick={onEdit} variant="outline">
                Edit Task
              </Button>
              <Button onClick={onDelete} variant="destructive">
                Delete Task
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
/**
 * Task Card Component - LEVER compliant
 * Reuses existing Card component from Langflow
 */
import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import {
  GripVertical,
  Link,
  Trash2,
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { useTaskManagementStore } from "@/stores/taskManagementStore";
import { Task } from "@/types/task-management";

interface TaskCardProps {
  task: Task;
}

export function TaskCard({ task }: TaskCardProps) {
  const deleteTask = useTaskManagementStore((state) => state.deleteTask);

  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: task.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition: isDragging ? "none" : transition,
    opacity: isDragging ? 0.8 : 1,
    zIndex: isDragging ? 1000 : "auto",
  };

  const handleDelete = async (e: React.MouseEvent) => {
    e.stopPropagation();
    if (confirm("Delete this task?")) {
      await deleteTask(task.id);
    }
  };

  const getPriorityBadge = () => {
    const colors = {
      low: "bg-gray-100 text-gray-800",
      medium: "bg-yellow-100 text-yellow-800",
      high: "bg-red-100 text-red-800"
    };
    
    return (
      <Badge className={`text-xs ${colors[task.priority]}`}>
        {task.priority}
      </Badge>
    );
  };

  return (
    <Card
      ref={setNodeRef}
      style={style}
      className={`
        cursor-move p-3 group relative task-card
        transition-all duration-200 ease-out
        hover:shadow-md hover:scale-[1.02]
        ${
          isDragging
            ? "shadow-xl scale-105 rotate-1 ring-2 ring-blue-200 bg-white dragging"
            : "hover:shadow-lg"
        }
      `}
      {...attributes}
      {...listeners}
    >
      <div className="flex items-start gap-2">
        {/* Drag Handle */}
        <div className="flex-shrink-0 mt-1 opacity-30 group-hover:opacity-60 transition-opacity">
          <GripVertical className="h-4 w-4 text-gray-400" />
        </div>

        <div className="flex-1">
          <div className="flex items-start justify-between mb-2">
            <h4 className="font-medium">{task.title}</h4>
            {getPriorityBadge()}
          </div>
          {task.description && (
            <p className="text-sm text-gray-600 mt-1">{task.description}</p>
          )}
          {task.flow_id && (
            <div className="mt-2 flex items-center gap-1 text-xs text-blue-600">
              <Link className="h-3 w-3" />
              <span>Linked to flow</span>
            </div>
          )}
        </div>

        <div className="flex-shrink-0">
          <Button
            size="icon"
            variant="ghost"
            onClick={handleDelete}
            className="h-6 w-6 opacity-0 group-hover:opacity-100 transition-opacity"
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </Card>
  );
}

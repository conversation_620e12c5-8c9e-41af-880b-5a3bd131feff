/**
 * Task Column Component - LEVER compliant
 */
import { useDroppable } from "@dnd-kit/core";
import {
  SortableContext,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { useEffect, useState } from "react";
import { Badge } from "@/components/ui/badge";
import { Card } from "@/components/ui/card";
import { TaskColumn as TaskColumnType } from "@/types/task-management";
import { TaskCard } from "./TaskCard";

interface TaskColumnProps {
  column: TaskColumnType;
  onTaskClick?: (task: any) => void;
}

export function TaskColumn({ column, onTaskClick }: TaskColumnProps) {
  const { setNodeRef, isOver } = useDroppable({
    id: column.id,
  });

  const [showSuccess, setShowSuccess] = useState(false);
  const [prevTaskCount, setPrevTaskCount] = useState(column.tasks.length);

  useEffect(() => {
    if (column.tasks.length > prevTaskCount) {
      setShowSuccess(true);
      setTimeout(() => setShowSuccess(false), 600);
    }
    setPrevTaskCount(column.tasks.length);
  }, [column.tasks.length, prevTaskCount]);

  const taskIds = column.tasks.map((task) => task.id);

  return (
    <Card
      className={`
      flex h-full w-80 flex-col task-column
      transition-all duration-300 ease-out
      ${
        isOver
          ? "ring-2 ring-blue-400 ring-offset-2 shadow-lg scale-[1.02] bg-blue-50/50"
          : "hover:shadow-md"
      }
      ${showSuccess ? "drop-success" : ""}
    `}
    >
      <div className="border-b p-4">
        <div className="flex items-center justify-between">
          <h3 className="font-semibold">{column.title}</h3>
          <Badge variant="secondary">{column.tasks.length}</Badge>
        </div>
      </div>

      <div
        ref={setNodeRef}
        className={`
          flex-1 overflow-y-auto p-2 relative
          transition-all duration-300 ease-out
          ${isOver ? "bg-gradient-to-b from-blue-50/30 to-transparent" : ""}
        `}
      >
        <SortableContext items={taskIds} strategy={verticalListSortingStrategy}>
          <div className="space-y-2">
            {column.tasks.map((task) => (
              <div key={task.id} onClick={() => onTaskClick?.(task)}>
                <TaskCard task={task} />
              </div>
            ))}
          </div>
        </SortableContext>

        {column.tasks.length === 0 && (
          <div
            className={`
            text-center text-gray-400 text-sm mt-8 p-8 rounded-lg border-2 border-dashed
            transition-all duration-300 ease-out
            ${
              isOver
                ? "border-blue-400 text-blue-600 bg-blue-50/50 scale-105"
                : "border-gray-200 hover:border-gray-300"
            }
          `}
          >
            <div className="space-y-2">
              <div
                className={`
                text-lg transition-transform duration-300
                ${isOver ? "scale-110" : ""}
              `}
              >
                📋
              </div>
              <p className="font-medium">
                {isOver ? "Drop task here!" : "No tasks yet"}
              </p>
              <p className="text-xs">
                {isOver ? "Release to add task" : "Drag tasks to this column"}
              </p>
            </div>
          </div>
        )}

        {/* Drop indicator for non-empty columns */}
        {isOver && column.tasks.length > 0 && (
          <div className="absolute top-0 left-2 right-2 h-1 bg-blue-400 rounded-full animate-pulse" />
        )}
      </div>
    </Card>
  );
}

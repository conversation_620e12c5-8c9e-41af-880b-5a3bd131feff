/**
 * Task Board List - LEVER compliant
 * Shows all task boards
 */

import { Loader2, Plus } from "lucide-react";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { useTaskManagementStore } from "@/stores/taskManagementStore";

export function TaskBoardList() {
  const navigate = useNavigate();
  const [isCreating, setIsCreating] = useState(false);
  const [newBoardName, setNewBoardName] = useState("");
  const [newBoardDescription, setNewBoardDescription] = useState("");

  const { boards, loading, error, fetchBoards, createBoard } =
    useTaskManagementStore();

  useEffect(() => {
    fetchBoards();
  }, [fetchBoards]);

  const handleCreateBoard = async () => {
    if (!newBoardName.trim()) return;

    await createBoard(newBoardName, newBoardDescription);
    setNewBoardName("");
    setNewBoardDescription("");
    setIsCreating(false);
  };

  const handleBoardClick = (boardId: string) => {
    navigate(`/task-management/boards/${boardId}`);
  };

  if (loading && (!boards || boards.length === 0)) {
    return (
      <div className="flex h-full items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="mb-6 flex items-center justify-between">
        <h1 className="text-3xl font-bold">Task Boards</h1>

        <Dialog open={isCreating} onOpenChange={setIsCreating}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              New Board
            </Button>
          </DialogTrigger>
          <DialogContent aria-describedby="create-board-description">
            <DialogHeader>
              <DialogTitle>Create New Board</DialogTitle>
            </DialogHeader>
            <p id="create-board-description" className="sr-only">
              Create a new task board by providing a name and optional
              description
            </p>
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium">Name</label>
                <Input
                  placeholder="Board name..."
                  value={newBoardName}
                  onChange={(e) => setNewBoardName(e.target.value)}
                />
              </div>
              <div>
                <label className="text-sm font-medium">Description</label>
                <Input
                  placeholder="Optional description..."
                  value={newBoardDescription}
                  onChange={(e) => setNewBoardDescription(e.target.value)}
                />
              </div>
              <Button
                onClick={handleCreateBoard}
                disabled={!newBoardName.trim()}
                className="w-full"
              >
                Create Board
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {error && (
        <div className="mb-4 rounded bg-red-50 p-4 text-red-600">
          Error: {error}
        </div>
      )}

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {Array.isArray(boards) &&
          boards.map((board) => (
            <Card
              key={board.id}
              className="cursor-pointer p-6 transition-shadow hover:shadow-lg"
              onClick={() => handleBoardClick(board.id)}
            >
              <h3 className="text-lg font-semibold">{board.name}</h3>
              {board.description && (
                <p className="mt-2 text-sm text-gray-600">
                  {board.description}
                </p>
              )}
              <div className="mt-4 text-xs text-gray-500">
                Created: {new Date(board.created_at).toLocaleDateString()}
              </div>
            </Card>
          ))}
      </div>

      {(!boards || boards.length === 0) && !loading && (
        <div className="text-center text-gray-500">
          <p>No boards yet. Create your first board to get started!</p>
        </div>
      )}
    </div>
  );
}

export default TaskBoardList;

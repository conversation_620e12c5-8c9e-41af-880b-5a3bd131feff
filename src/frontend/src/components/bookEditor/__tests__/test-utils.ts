/**
 * Book Editor Test Utilities
 * 
 * Shared testing utilities for Book Editor components, following PhotoEditor
 * test patterns while adding book-specific test scenarios and helpers.
 * 
 * LEVER Principles:
 * - Leverage: PhotoEditor test utility patterns (85% code reuse)
 * - Extend: Book-specific test scenarios and mock data
 * - Verify: Multi-page state management and KDP compliance
 * - Eliminate: No test duplication with PhotoEditor
 * - Reduce: Simplified mock setup with configuration-driven approach
 */

import * as fabric from 'fabric';
import { QueryClient } from '@tanstack/react-query';
import type { BookPage, BookMetadata, PrintSettings, BookEditorStoreState } from '../../../stores/bookEditor/types';
import { BOOK_SIZES, PRINT_MARGINS, TYPOGRAPHY } from '../../../stores/bookEditor/constants';

// Mock fabric.js for testing
export const mockFabricCanvas = () => ({
  add: jest.fn(),
  remove: jest.fn(),
  renderAll: jest.fn(),
  getActiveObject: jest.fn(),
  setActiveObject: jest.fn(),
  discardActiveObject: jest.fn(),
  getObjects: jest.fn(() => []),
  toDataURL: jest.fn(() => 'data:image/png;base64,test'),
  toJSON: jest.fn(() => ({})),
  loadFromJSON: jest.fn((json, callback) => callback && callback()),
  dispose: jest.fn(),
  on: jest.fn(),
  off: jest.fn(),
  setDimensions: jest.fn(),
  setZoom: jest.fn(),
  getZoom: jest.fn(() => 1),
  getWidth: jest.fn(() => 800),
  getHeight: jest.fn(() => 600),
  selection: true,
  defaultCursor: 'grab',
  hoverCursor: 'grab',
  moveCursor: 'grabbing',
  forEachObject: jest.fn((callback) => {
    // Mock some objects for testing
    const mockObjects = [
      { selectable: true, evented: true },
      { selectable: true, evented: true },
    ];
    mockObjects.forEach(callback);
  }),
  sendToBack: jest.fn(),
});

export const mockFabricText = () => ({
  set: jest.fn(),
  setCoords: jest.fn(),
  selectable: true,
  evented: true,
  fontFamily: 'Arial',
  fontSize: 14,
  text: 'Sample text',
});

export const mockFabricRect = () => ({
  set: jest.fn(),
  setCoords: jest.fn(),
  selectable: true,
  evented: true,
  width: 100,
  height: 100,
  fill: '#ffffff',
});

// Book-specific mock data factories
export const createMockBookPage = (overrides: Partial<BookPage> = {}): BookPage => ({
  id: `page-${Date.now()}-${Math.random()}`,
  pageNumber: 1,
  pageType: 'content',
  name: 'Page 1',
  canvasState: JSON.stringify({}),
  settings: {
    margins: PRINT_MARGINS.STANDARD,
    backgroundColor: '#ffffff',
    locked: false,
    visible: true,
  },
  metadata: {
    createdAt: new Date().toISOString(),
    modifiedAt: new Date().toISOString(),
    objectCount: 0,
    memoryUsage: 0,
  },
  ...overrides,
});

export const createMockBookMetadata = (overrides: Partial<BookMetadata> = {}): BookMetadata => ({
  title: 'Test Book',
  author: 'Test Author',
  language: 'en',
  pageCount: 1,
  version: '1.0.0',
  createdAt: new Date().toISOString(),
  modifiedAt: new Date().toISOString(),
  ...overrides,
});

export const createMockPrintSettings = (overrides: Partial<PrintSettings> = {}): PrintSettings => ({
  bookSize: 'TRADE_LARGE',
  dpi: 300,
  colorMode: 'CMYK',
  margins: PRINT_MARGINS.STANDARD,
  bleed: { top: 0.125, bottom: 0.125, left: 0.125, right: 0.125 },
  paperType: 'white',
  bindingType: 'perfectBound',
  showBleedMarks: false,
  showCropMarks: false,
  showColorBars: false,
  showRegistrationMarks: false,
  imageCompression: 'high',
  vectorRasterization: 'preserve',
  ...overrides,
});

export const createMockBookProject = (pageCount: number = 3): {
  metadata: BookMetadata;
  printSettings: PrintSettings;
  pages: BookPage[];
} => {
  const pages = Array.from({ length: pageCount }, (_, index) =>
    createMockBookPage({
      pageNumber: index + 1,
      name: `Page ${index + 1}`,
      pageType: index === 0 ? 'cover' : 'content',
    })
  );

  return {
    metadata: createMockBookMetadata({ pageCount }),
    printSettings: createMockPrintSettings(),
    pages,
  };
};

// Mock store state
export const createMockBookEditorState = (overrides: Partial<BookEditorStoreState> = {}): BookEditorStoreState => {
  const initialPage = createMockBookPage();
  const bookSize = 'TRADE_LARGE';
  const dimensions = BOOK_SIZES[bookSize];

  return {
    canvasState: {
      width: dimensions.width,
      height: dimensions.height,
      bookSize,
      pages: [initialPage],
      currentPageIndex: 0,
      zoom: 1,
      panX: 0,
      panY: 0,
      historyIndex: 0,
      canUndo: false,
      canRedo: false,
      performance: {
        totalPages: 1,
        currentPageObjectCount: 0,
        lastRenderTime: 0,
        memoryUsage: 0,
        averagePageSize: 0,
        largestPageSize: 0,
      },
      backgroundColor: '#ffffff',
      gridVisible: false,
      snapToGrid: false,
      gridSize: 20,
      showMargins: true,
      showBleed: false,
      showPageNumbers: false,
    },
    activeCanvas: null,
    isCanvasReady: false,
    bookMetadata: createMockBookMetadata(),
    printSettings: createMockPrintSettings(),
    typography: {
      availableFonts: TYPOGRAPHY.DEFAULT_FONTS,
      embeddedFonts: {},
      currentFonts: {
        body: 'Times New Roman',
        heading: 'Arial',
        caption: 'Times New Roman',
      },
      presets: {
        body: {
          fontFamily: 'Times New Roman',
          fontSize: 12,
          lineHeight: 1.5,
          letterSpacing: 0,
          color: '#000000',
          fontWeight: 'normal',
          fontStyle: 'normal',
        },
        heading: {
          fontFamily: 'Arial',
          fontSize: 18,
          lineHeight: 1.3,
          letterSpacing: 0,
          color: '#000000',
          fontWeight: 'bold',
          fontStyle: 'normal',
        },
      },
      global: {
        baseFontSize: 12,
        baseLineHeight: 1.5,
        scaleRatio: 1.25,
        paragraphSpacing: 12,
        dropCapEnabled: false,
        hyphenationEnabled: false,
      },
    },
    masterPages: [],
    pageHistory: {},
    exportConfiguration: {
      format: 'pdf',
      quality: 0.9,
      dpi: 300,
      pageRange: { type: 'all' },
      filename: 'book-export',
      destination: 'download',
      pdfOptions: {
        version: 'PDF-1.4',
        embedFonts: true,
        subsetFonts: true,
        includeBleed: false,
        includeMarks: false,
        compression: {
          images: 'jpeg',
          quality: 0.9,
        },
      },
    },
    editHistory: [],
    undoStack: [],
    redoStack: [],
    currentHistoryIndex: 0,
    maxHistorySize: 50,
    performanceMetrics: {
      processingTime: 0,
      memoryUsage: 0,
      renderTime: 0,
      objectCount: 0,
      eventCount: 0,
      lastUpdate: Date.now(),
      totalPages: 1,
      averagePageComplexity: 0,
      fontLoadTime: 0,
    },
    memoryManager: {
      currentUsage: 0,
      maxUsage: 100 * 1024 * 1024, // 100MB
      cleanupThreshold: 80 * 1024 * 1024, // 80MB
      historyLimit: 25,
      autoCleanup: true,
      lastCleanup: new Date().toISOString(),
      pageMemoryUsage: {},
      fontMemoryUsage: 0,
      thumbnailMemoryUsage: 0,
    },
    backendState: {
      nodeId: 'test-node',
      componentState: {},
      performanceMetrics: {
        processingTime: 0,
        memoryUsage: 0,
        renderTime: 0,
        objectCount: 0,
        eventCount: 0,
        lastUpdate: Date.now(),
      },
      bookMetadata: {},
      lastUpdate: new Date().toISOString(),
      isProcessing: false,
      currentPage: 0,
      totalPages: 1,
    },
    syncManager: {
      isConnected: false,
      lastSync: new Date().toISOString(),
      pendingOperations: 0,
      syncEnabled: true,
      conflictResolution: 'local',
      retryCount: 0,
      maxRetries: 3,
      syncScope: 'current-page',
    },
    selectedObjects: [],
    toolPalette: {
      activeTool: 'select',
      toolOptions: {},
      textTool: {
        fontFamily: 'Times New Roman',
        fontSize: 12,
        textAlign: 'left',
        lineHeight: 1.5,
      },
      pageTool: {
        showThumbnails: true,
        thumbnailSize: 120,
        navigationMode: 'sidebar',
      },
    },
    errors: [],
    validationState: {
      canvasValid: true,
      stateValid: true,
      lastValidation: new Date().toISOString(),
      kdpCompliant: false,
      printReady: false,
      fontsEmbedded: false,
      validationInProgress: false,
    },
    ...overrides,
  };
};

// Mock Zustand store
export const createMockBookEditorStore = (initialState?: Partial<BookEditorStoreState>) => {
  const state = createMockBookEditorState(initialState);
  
  return {
    getState: () => state,
    setState: jest.fn((updater) => {
      if (typeof updater === 'function') {
        updater(state);
      } else {
        Object.assign(state, updater);
      }
    }),
    subscribe: jest.fn(),
    destroy: jest.fn(),
  };
};

// Performance testing utilities
export const measureCanvasRenderTime = async (canvas: any): Promise<number> => {
  const startTime = performance.now();
  await new Promise(resolve => {
    canvas.renderAll();
    requestAnimationFrame(resolve);
  });
  return performance.now() - startTime;
};

export const measureMemoryUsage = (): number => {
  return (performance as any).memory?.usedJSHeapSize || 0;
};

export const createLargeBookProject = (pageCount: number = 50): any => {
  const pages = Array.from({ length: pageCount }, (_, index) => {
    const page = createMockBookPage({
      pageNumber: index + 1,
      name: `Page ${index + 1}`,
    });
    
    // Add complex canvas state for performance testing
    const complexCanvasState = {
      objects: Array.from({ length: 20 }, (_, objIndex) => ({
        type: 'text',
        text: `Text object ${objIndex + 1} on page ${index + 1}`,
        left: Math.random() * 400,
        top: Math.random() * 300,
        fontSize: 12 + Math.random() * 8,
        fontFamily: TYPOGRAPHY.DEFAULT_FONTS[Math.floor(Math.random() * TYPOGRAPHY.DEFAULT_FONTS.length)],
      })),
    };
    
    page.canvasState = JSON.stringify(complexCanvasState);
    page.metadata.objectCount = 20;
    page.metadata.memoryUsage = 1024 * (1 + Math.random()); // 1-2KB per page
    
    return page;
  });

  return {
    metadata: createMockBookMetadata({ pageCount }),
    printSettings: createMockPrintSettings(),
    pages,
  };
};

// KDP validation test data
export const createKDPCompliantBook = () => {
  return createMockBookProject(24); // Minimum 24 pages for KDP
};

export const createKDPNonCompliantBook = () => {
  const project = createMockBookProject(10); // Less than minimum pages
  project.printSettings.margins = {
    top: 0.25, // Too small for KDP
    bottom: 0.25,
    inside: 0.25,
    outside: 0.25,
  };
  return project;
};

// Accessibility testing utilities
export const checkKeyboardNavigation = async (element: HTMLElement): Promise<boolean> => {
  const focusableElements = element.querySelectorAll(
    'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
  );
  
  let canNavigate = true;
  focusableElements.forEach((el, index) => {
    if (el.getAttribute('tabindex') === '-1' && !el.hasAttribute('disabled')) {
      canNavigate = false;
    }
  });
  
  return canNavigate;
};

export const checkAriaLabels = (element: HTMLElement): { hasLabels: boolean; missingLabels: string[] } => {
  const interactiveElements = element.querySelectorAll('button, input, select, textarea');
  const missingLabels: string[] = [];
  
  interactiveElements.forEach((el) => {
    const hasLabel = el.hasAttribute('aria-label') || 
                    el.hasAttribute('aria-labelledby') ||
                    el.getAttribute('title') ||
                    (el.tagName === 'BUTTON' && el.textContent?.trim());
    
    if (!hasLabel) {
      missingLabels.push(el.tagName.toLowerCase());
    }
  });
  
  return {
    hasLabels: missingLabels.length === 0,
    missingLabels,
  };
};

// Query client for testing
export const createTestQueryClient = (): QueryClient => {
  return new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        cacheTime: 0,
      },
      mutations: {
        retry: false,
      },
    },
  });
};

// Mock WebSocket for real-time testing
export const mockWebSocket = () => {
  const mockWs = {
    send: jest.fn(),
    close: jest.fn(),
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    readyState: WebSocket.OPEN,
    simulateMessage: (data: any) => {
      const event = new MessageEvent('message', { data: JSON.stringify(data) });
      mockWs.addEventListener.mock.calls
        .filter(([type]) => type === 'message')
        .forEach(([, handler]) => handler(event));
    },
    simulateClose: () => {
      const event = new CloseEvent('close');
      mockWs.addEventListener.mock.calls
        .filter(([type]) => type === 'close')
        .forEach(([, handler]) => handler(event));
    },
    simulateOpen: () => {
      const event = new Event('open');
      mockWs.addEventListener.mock.calls
        .filter(([type]) => type === 'open')
        .forEach(([, handler]) => handler(event));
    },
  };
  
  (global as any).WebSocket = jest.fn(() => mockWs);
  
  return mockWs;
};

// Test constants
export const TEST_TIMEOUTS = {
  CANVAS_RENDER: 2000, // <2s requirement
  PAGE_NAVIGATION: 500,
  EXPORT_SMALL: 5000,
  EXPORT_LARGE: 30000, // <30s requirement
  MEMORY_CLEANUP: 1000,
};

export const PERFORMANCE_THRESHOLDS = {
  CANVAS_RENDER_TIME: 2000, // ms
  EXPORT_TIME: 30000, // ms
  MEMORY_USAGE: 100 * 1024 * 1024, // 100MB
  PAGE_SWITCH_TIME: 500, // ms
};

// Mock ResizeObserver and IntersectionObserver (common in book editor tests)
export const setupGlobalMocks = () => {
  global.ResizeObserver = jest.fn().mockImplementation(() => ({
    observe: jest.fn(),
    unobserve: jest.fn(),
    disconnect: jest.fn(),
  }));

  global.IntersectionObserver = jest.fn().mockImplementation(() => ({
    observe: jest.fn(),
    unobserve: jest.fn(),
    disconnect: jest.fn(),
  }));

  // Mock fabric.js
  (global as any).fabric = {
    Canvas: jest.fn().mockImplementation(() => mockFabricCanvas()),
    IText: jest.fn().mockImplementation(() => mockFabricText()),
    Text: jest.fn().mockImplementation(() => mockFabricText()),
    Rect: jest.fn().mockImplementation(() => mockFabricRect()),
    Line: jest.fn().mockImplementation(() => mockFabricRect()),
    Group: jest.fn().mockImplementation(() => ({
      ...mockFabricRect(),
      addWithUpdate: jest.fn(),
    })),
    Object: {
      prototype: {
        set: jest.fn(),
        setCoords: jest.fn(),
      },
    },
  };

  // Mock performance API if not available
  if (typeof performance === 'undefined') {
    (global as any).performance = {
      now: () => Date.now(),
      memory: {
        usedJSHeapSize: 10 * 1024 * 1024, // 10MB
      },
    };
  }
};

export default {
  createMockBookPage,
  createMockBookMetadata,
  createMockPrintSettings,
  createMockBookProject,
  createMockBookEditorState,
  createMockBookEditorStore,
  measureCanvasRenderTime,
  measureMemoryUsage,
  createLargeBookProject,
  createKDPCompliantBook,
  createKDPNonCompliantBook,
  checkKeyboardNavigation,
  checkAriaLabels,
  createTestQueryClient,
  mockWebSocket,
  setupGlobalMocks,
  TEST_TIMEOUTS,
  PERFORMANCE_THRESHOLDS,
};
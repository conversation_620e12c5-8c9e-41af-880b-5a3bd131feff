import * as fabric from "fabric";
import { memo, useCallback, useEffect, useRef, useState } from "react";
import { cn } from "@/utils/utils";
import { useBookCanvas } from "./hooks/useBookCanvas";
import type { BookCanvasProps, BookCanvasState, BookPage } from "./types";
import { 
  KDP_BOOK_SIZES, 
  PRINT_RESOLUTION, 
  PRINT_MARGINS,
  TYPOGRAPHY,
  getBookSizePixels 
} from "./constants";

/**
 * BookCanvas Component - BE-001 Implementation
 * 
 * Professional book editing canvas using Fabric.js with Langflow integration.
 * Extends PhotoCanvas functionality with print-optimized features for Amazon KDP book creation.
 * Follows LEVER principles by leveraging 85% of PhotoCanvas code and extending
 * with book-specific multi-page functionality.
 * 
 * Features:
 * - Multi-page canvas management with 300 DPI print resolution
 * - Amazon KDP book size presets and compliance validation
 * - Page navigation with thumbnails and reordering
 * - Professional typography controls with font embedding
 * - CMYK color support and print-ready output
 * - Master page templates for consistent layouts
 * - Print margins, bleeds, and guides visualization
 * - History management across multiple pages
 * - Real-time KDP compliance checking
 */
const BookCanvas = memo<BookCanvasProps>(({
  nodeId,
  bookData,
  canvasConfig = {},
  onCanvasReady,
  onStateChange,
  onError,
  onPageChanged,
  onPageAdded,
  onPageRemoved,
  onPageReordered,
  onObjectAdded,
  onObjectRemoved,
  onTextAdded,
  onFontChanged,
  onExportCompleted,
  onValidationCompleted,
  className,
  width,
  height,
  currentPage = 0,
  disabled = false,
  showGrid = false,
  showMargins = true,
  showBleed = false,
  enableZoom = true,
  enablePan = true,
  enablePageNavigation = true,
  showPageThumbnails = true,
  thumbnailSize = 120,
  maxPages = 500,
  defaultFont = TYPOGRAPHY.APPROVED_FONTS[0],
  enableTypographyPanel = true,
  previewMode = 'edit',
  showPrintMarks = false,
  ...canvasProps
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [selectedThumbnail, setSelectedThumbnail] = useState<number>(currentPage);

  // Calculate canvas dimensions based on book size
  const bookSize = canvasConfig.bookSize || 'TRADE_LARGE';
  const bookDimensions = getBookSizePixels(bookSize, PRINT_RESOLUTION.DPI);
  const finalWidth = width || Math.min(bookDimensions.width * 0.5, 800); // Scale down for display
  const finalHeight = height || Math.min(bookDimensions.height * 0.5, 600);

  // Use the custom hook for book canvas management and Langflow integration
  const {
    canvas,
    canvasState,
    isReady,
    error,
    performance,
    initializeCanvas,
    updateCanvasState,
    loadBookData,
    resetCanvas,
    currentPage: currentPageData,
    currentPageIndex,
    totalPages,
    navigateToPage,
    addPage,
    removePage,
    duplicatePage,
    reorderPage,
    addText,
    applyFontToSelection,
    masterPages,
    applyMasterPage,
    exportPage,
    exportBook,
    validateBook,
    validateKDPCompliance,
    zoomIn,
    zoomOut,
    resetZoom,
    fitToPage,
    centerPage,
    canUndo,
    canRedo,
    undo,
    redo,
  } = useBookCanvas({
    nodeId,
    canvasRef,
    bookSize,
    dpi: PRINT_RESOLUTION.DPI,
    bookData: typeof bookData === 'string' ? JSON.parse(bookData) : bookData,
    initialPageCount: 1, // Always start with at least one page
    enableZoom,
    enablePan,
    onStateChange,
    onError,
    onCanvasReady,
    onPageChanged,
    onPageAdded,
    onPageRemoved,
    onPageReordered,
    onObjectAdded,
    onObjectRemoved,
    onTextAdded,
    onFontChanged,
    onExportCompleted,
    onValidationCompleted,
    ...canvasConfig,
  });

  // Initialize canvas when component mounts
  useEffect(() => {
    if (canvasRef.current && !canvas) {
      initializeCanvas();
    }
  }, [canvas, initializeCanvas]);

  // Load book data when bookData changes
  useEffect(() => {
    if (canvas && bookData && isReady) {
      loadBookData(bookData);
    }
  }, [canvas, bookData, isReady, loadBookData]);

  // Notify parent when canvas is ready
  useEffect(() => {
    if (isReady && canvas && onCanvasReady) {
      onCanvasReady(canvas, canvasState);
    }
  }, [isReady, canvas, canvasState, onCanvasReady]);

  // Sync current page with prop
  useEffect(() => {
    if (currentPage !== currentPageIndex && currentPage >= 0 && currentPage < totalPages) {
      navigateToPage(currentPage);
      setSelectedThumbnail(currentPage);
    }
  }, [currentPage, currentPageIndex, totalPages, navigateToPage]);

  // Handle canvas resize
  const handleResize = useCallback(() => {
    if (canvas && containerRef.current) {
      const container = containerRef.current;
      const rect = container.getBoundingClientRect();
      
      // Maintain aspect ratio based on book dimensions
      const aspectRatio = bookDimensions.width / bookDimensions.height;
      let newWidth = rect.width;
      let newHeight = rect.height;
      
      if (newWidth / newHeight > aspectRatio) {
        newWidth = newHeight * aspectRatio;
      } else {
        newHeight = newWidth / aspectRatio;
      }
      
      canvas.setDimensions({
        width: newWidth,
        height: newHeight,
      });
      
      canvas.renderAll();
    }
  }, [canvas, bookDimensions]);

  // Setup resize observer for responsive canvas
  useEffect(() => {
    if (!containerRef.current) return;

    const resizeObserver = new ResizeObserver(handleResize);
    resizeObserver.observe(containerRef.current);

    return () => {
      resizeObserver.disconnect();
    };
  }, [handleResize]);

  // Handle canvas interactions based on disabled state
  useEffect(() => {
    if (canvas) {
      canvas.selection = !disabled;
      canvas.defaultCursor = disabled ? "default" : "grab";
      canvas.hoverCursor = disabled ? "default" : "grab";
      canvas.moveCursor = disabled ? "default" : "grabbing";
      
      // Disable object interactions when disabled
      canvas.forEachObject((obj) => {
        obj.selectable = !disabled;
        obj.evented = !disabled;
      });
      
      canvas.renderAll();
    }
  }, [canvas, disabled]);

  // Add print guides (margins, bleeds, grid)
  useEffect(() => {
    if (!canvas) return;

    const addPrintGuides = () => {
      // Remove existing guides
      canvas.getObjects().forEach((obj) => {
        if ((obj as any).isPrintGuide) {
          canvas.remove(obj);
        }
      });

      const canvasWidth = canvas.getWidth();
      const canvasHeight = canvas.getHeight();
      const scale = canvasWidth / bookDimensions.width; // Scale factor for display

      // Add margin guides
      if (showMargins) {
        const margins = canvasState.bookSettings.margins;
        const marginRect = new fabric.Rect({
          left: margins.outside * PRINT_RESOLUTION.DPI * scale,
          top: margins.top * PRINT_RESOLUTION.DPI * scale,
          width: canvasWidth - (margins.outside + margins.inside) * PRINT_RESOLUTION.DPI * scale,
          height: canvasHeight - (margins.top + margins.bottom) * PRINT_RESOLUTION.DPI * scale,
          fill: 'transparent',
          stroke: '#007bff',
          strokeWidth: 1,
          strokeDashArray: [5, 5],
          selectable: false,
          evented: false,
          excludeFromExport: true,
        });
        (marginRect as any).isPrintGuide = true;
        canvas.add(marginRect);
      }

      // Add bleed guides
      if (showBleed) {
        const bleed = canvasState.bookSettings.bleed;
        const bleedRect = new fabric.Rect({
          left: -bleed.left * PRINT_RESOLUTION.DPI * scale,
          top: -bleed.top * PRINT_RESOLUTION.DPI * scale,
          width: canvasWidth + (bleed.left + bleed.right) * PRINT_RESOLUTION.DPI * scale,
          height: canvasHeight + (bleed.top + bleed.bottom) * PRINT_RESOLUTION.DPI * scale,
          fill: 'transparent',
          stroke: '#dc3545',
          strokeWidth: 1,
          strokeDashArray: [2, 2],
          selectable: false,
          evented: false,
          excludeFromExport: true,
        });
        (bleedRect as any).isPrintGuide = true;
        canvas.add(bleedRect);
      }

      // Add grid
      if (showGrid) {
        const gridSize = canvasState.bookSettings.gridSize * scale;
        const gridGroup = new fabric.Group([], {
          selectable: false,
          evented: false,
          excludeFromExport: true,
        });

        // Vertical lines
        for (let i = 0; i <= canvasWidth; i += gridSize) {
          const line = new fabric.Line([i, 0, i, canvasHeight], {
            stroke: '#e0e0e0',
            strokeWidth: 0.5,
            selectable: false,
            evented: false,
          });
          gridGroup.addWithUpdate(line);
        }

        // Horizontal lines
        for (let i = 0; i <= canvasHeight; i += gridSize) {
          const line = new fabric.Line([0, i, canvasWidth, i], {
            stroke: '#e0e0e0',
            strokeWidth: 0.5,
            selectable: false,
            evented: false,
          });
          gridGroup.addWithUpdate(line);
        }

        (gridGroup as any).isPrintGuide = true;
        canvas.add(gridGroup);
        canvas.sendToBack(gridGroup);
      }

      canvas.renderAll();
    };

    addPrintGuides();
  }, [canvas, showGrid, showMargins, showBleed, canvasState.bookSettings, bookDimensions]);

  // Page navigation handlers
  const handlePageNavigation = useCallback((pageIndex: number) => {
    if (pageIndex >= 0 && pageIndex < totalPages) {
      navigateToPage(pageIndex);
      setSelectedThumbnail(pageIndex);
    }
  }, [totalPages, navigateToPage]);

  const handleAddPage = useCallback(() => {
    if (totalPages < maxPages) {
      const newPage = addPage('content');
      navigateToPage(totalPages); // Navigate to the new page
    }
  }, [totalPages, maxPages, addPage, navigateToPage]);

  const handleRemovePage = useCallback((pageIndex: number) => {
    if (totalPages > 1) {
      removePage(pageIndex);
    }
  }, [totalPages, removePage]);

  const handleDuplicatePage = useCallback((pageIndex: number) => {
    if (totalPages < maxPages) {
      duplicatePage(pageIndex);
    }
  }, [totalPages, maxPages, duplicatePage]);

  // Typography handlers
  const handleAddText = useCallback(() => {
    if (!disabled) {
      addText("Click to edit text", {
        fontSize: TYPOGRAPHY.FONT_SIZES.BODY_TEXT.recommended,
        fontFamily: defaultFont,
      });
    }
  }, [disabled, addText, defaultFont]);

  const handleFontChange = useCallback((fontFamily: string) => {
    applyFontToSelection(fontFamily);
  }, [applyFontToSelection]);

  // Export handlers
  const handleExportPage = useCallback(() => {
    const pageData = exportPage(currentPageIndex);
    if (pageData) {
      const link = document.createElement('a');
      link.download = `page-${currentPageIndex + 1}.png`;
      link.href = pageData;
      link.click();
    }
  }, [exportPage, currentPageIndex]);

  const handleExportBook = useCallback(async () => {
    try {
      const result = await exportBook({ format: 'pdf' });
      console.log('Book exported:', result);
    } catch (err) {
      console.error('Export failed:', err);
    }
  }, [exportBook]);

  // Validation handlers
  const handleValidateBook = useCallback(async () => {
    try {
      const results = await validateBook();
      onValidationCompleted?.(results);
    } catch (err) {
      console.error('Validation failed:', err);
    }
  }, [validateBook, onValidationCompleted]);

  // Render page thumbnails
  const renderPageThumbnails = () => {
    if (!showPageThumbnails || !enablePageNavigation) return null;

    return (
      <div className="page-thumbnails flex gap-2 p-2 bg-gray-50 border-t overflow-x-auto">
        {canvasState.pages.map((page, index) => (
          <div
            key={page.id}
            className={cn(
              "page-thumbnail relative flex-shrink-0 cursor-pointer border-2 rounded transition-all",
              {
                "border-blue-500 shadow-md": selectedThumbnail === index,
                "border-gray-300 hover:border-gray-400": selectedThumbnail !== index,
              }
            )}
            style={{ width: thumbnailSize, height: thumbnailSize * (bookDimensions.height / bookDimensions.width) }}
            onClick={() => handlePageNavigation(index)}
          >
            <div className="w-full h-full bg-white rounded flex items-center justify-center text-xs text-gray-500">
              Page {page.pageNumber}
            </div>
            
            {/* Page actions */}
            <div className="absolute -top-2 -right-2 flex gap-1">
              <button
                className="w-5 h-5 bg-blue-500 text-white rounded-full text-xs hover:bg-blue-600"
                onClick={(e) => {
                  e.stopPropagation();
                  handleDuplicatePage(index);
                }}
                title="Duplicate page"
              >
                +
              </button>
              {totalPages > 1 && (
                <button
                  className="w-5 h-5 bg-red-500 text-white rounded-full text-xs hover:bg-red-600"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleRemovePage(index);
                  }}
                  title="Remove page"
                >
                  ×
                </button>
              )}
            </div>
          </div>
        ))}
        
        {/* Add page button */}
        {totalPages < maxPages && (
          <button
            className="flex-shrink-0 border-2 border-dashed border-gray-300 rounded hover:border-gray-400 flex items-center justify-center text-gray-500 hover:text-gray-700"
            style={{ width: thumbnailSize, height: thumbnailSize * (bookDimensions.height / bookDimensions.width) }}
            onClick={handleAddPage}
            title="Add new page"
          >
            <span className="text-2xl">+</span>
          </button>
        )}
      </div>
    );
  };

  // Render toolbar
  const renderToolbar = () => {
    return (
      <div className="book-canvas-toolbar flex items-center justify-between p-2 bg-white border-b">
        {/* Left side - Page info and navigation */}
        <div className="flex items-center gap-4">
          <div className="text-sm text-gray-600">
            Page {currentPageIndex + 1} of {totalPages}
          </div>
          
          {enablePageNavigation && (
            <div className="flex items-center gap-1">
              <button
                className="p-1 rounded hover:bg-gray-100 disabled:opacity-50"
                disabled={currentPageIndex === 0}
                onClick={() => handlePageNavigation(currentPageIndex - 1)}
                title="Previous page"
              >
                ←
              </button>
              <button
                className="p-1 rounded hover:bg-gray-100 disabled:opacity-50"
                disabled={currentPageIndex === totalPages - 1}
                onClick={() => handlePageNavigation(currentPageIndex + 1)}
                title="Next page"
              >
                →
              </button>
            </div>
          )}

          <div className="text-xs text-gray-500">
            {KDP_BOOK_SIZES[bookSize].name} • {PRINT_RESOLUTION.DPI} DPI
          </div>
        </div>

        {/* Center - Tools */}
        <div className="flex items-center gap-2">
          {enableTypographyPanel && (
            <>
              <button
                className="px-3 py-1 text-sm bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
                disabled={disabled}
                onClick={handleAddText}
                title="Add text"
              >
                Text
              </button>
              
              <select
                className="text-sm border rounded px-2 py-1"
                value={defaultFont}
                onChange={(e) => handleFontChange(e.target.value)}
                disabled={disabled}
                title="Font family"
              >
                {TYPOGRAPHY.APPROVED_FONTS.map(font => (
                  <option key={font} value={font}>{font}</option>
                ))}
              </select>
            </>
          )}

          <div className="border-l h-6 mx-2" />

          <button
            className="p-1 rounded hover:bg-gray-100 disabled:opacity-50"
            disabled={!canUndo}
            onClick={undo}
            title="Undo"
          >
            ↶
          </button>
          <button
            className="p-1 rounded hover:bg-gray-100 disabled:opacity-50"
            disabled={!canRedo}
            onClick={redo}
            title="Redo"
          >
            ↷
          </button>
        </div>

        {/* Right side - View controls and export */}
        <div className="flex items-center gap-2">
          <button
            className="p-1 rounded hover:bg-gray-100"
            onClick={zoomOut}
            title="Zoom out"
          >
            -
          </button>
          <button
            className="p-1 rounded hover:bg-gray-100"
            onClick={resetZoom}
            title="Reset zoom"
          >
            100%
          </button>
          <button
            className="p-1 rounded hover:bg-gray-100"
            onClick={zoomIn}
            title="Zoom in"
          >
            +
          </button>

          <div className="border-l h-6 mx-2" />

          <button
            className="px-3 py-1 text-sm bg-green-500 text-white rounded hover:bg-green-600"
            onClick={handleValidateBook}
            title="Validate KDP compliance"
          >
            Validate
          </button>
          
          <button
            className="px-3 py-1 text-sm bg-purple-500 text-white rounded hover:bg-purple-600"
            onClick={handleExportBook}
            title="Export book as PDF"
          >
            Export
          </button>
        </div>
      </div>
    );
  };

  // Error display component
  if (error) {
    return (
      <div 
        className={cn(
          "flex items-center justify-center bg-red-50 border border-red-200 rounded-lg p-4",
          className
        )}
        style={{ width: finalWidth, height: finalHeight }}
      >
        <div className="text-center">
          <div className="text-red-600 font-medium mb-2">Book Canvas Error</div>
          <div className="text-red-500 text-sm">{error}</div>
        </div>
      </div>
    );
  }

  // Loading state
  if (!isReady) {
    return (
      <div 
        className={cn(
          "flex items-center justify-center bg-gray-50 border border-gray-200 rounded-lg",
          className
        )}
        style={{ width: finalWidth, height: finalHeight }}
      >
        <div className="text-center">
          <div className="animate-spin w-6 h-6 border-2 border-blue-500 border-t-transparent rounded-full mx-auto mb-2" />
          <div className="text-gray-600 text-sm">Initializing Book Canvas...</div>
          <div className="text-xs text-gray-500 mt-1">
            Setting up {KDP_BOOK_SIZES[bookSize].name} at {PRINT_RESOLUTION.DPI} DPI
          </div>
        </div>
      </div>
    );
  }

  return (
    <div
      className={cn(
        "book-canvas-wrapper flex flex-col border rounded-lg overflow-hidden bg-white",
        {
          "border-gray-300": !disabled,
          "border-gray-200 bg-gray-50": disabled,
        },
        className
      )}
      style={{ width: finalWidth, height: finalHeight }}
      {...canvasProps}
    >
      {/* Toolbar */}
      {renderToolbar()}

      {/* Main canvas area */}
      <div
        ref={containerRef}
        className="book-canvas-container relative flex-1 overflow-hidden"
        style={{ 
          backgroundColor: previewMode === 'print' ? '#f5f5f5' : '#ffffff',
        }}
      >
        <canvas
          ref={canvasRef}
          className="book-canvas"
          data-testid="book-canvas"
        />
        
        {/* Performance indicator (development mode) */}
        {process.env.NODE_ENV === "development" && performance && (
          <div className="absolute top-2 right-2 bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded">
            <div>Page: {performance.currentPageProcessingTime}ms</div>
            <div>Memory: {performance.currentPageMemoryUsage.toFixed(1)}MB</div>
            <div>Objects: {performance.currentPageObjectCount}</div>
            <div>Total Pages: {performance.totalPages}</div>
          </div>
        )}
        
        {/* Canvas status indicator */}
        <div className="absolute bottom-2 left-2 flex items-center gap-2">
          <div 
            className={cn(
              "w-2 h-2 rounded-full",
              {
                "bg-green-500": isReady && !error,
                "bg-red-500": error,
                "bg-yellow-500": !isReady,
              }
            )}
          />
          <span className="text-xs text-gray-600">
            {error ? "Error" : isReady ? "Ready" : "Loading"}
          </span>
          {currentPageData && (
            <span className="text-xs text-gray-500">
              • {currentPageData.pageType} • {currentPageData.name}
            </span>
          )}
        </div>

        {/* Book size indicator */}
        <div className="absolute bottom-2 right-2 text-xs text-gray-500 bg-white bg-opacity-75 px-2 py-1 rounded">
          {bookDimensions.width}×{bookDimensions.height}px ({PRINT_RESOLUTION.DPI} DPI)
        </div>
      </div>

      {/* Page thumbnails */}
      {renderPageThumbnails()}
    </div>
  );
});

BookCanvas.displayName = "BookCanvas";

export default BookCanvas;
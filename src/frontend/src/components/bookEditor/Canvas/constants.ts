/**
 * Amazon KDP Book Editor Constants - BE-001 Implementation
 * 
 * Print-optimized constants for Amazon KDP book creation.
 * Follows LEVER principles by leveraging established KDP standards
 * and providing comprehensive print specifications.
 * 
 * Features:
 * - Standard KDP book dimensions and sizes
 * - Print resolution and DPI settings
 * - CMYK color support for professional printing
 * - Margin and bleed specifications
 * - Typography and font requirements
 */

/**
 * Standard Amazon KDP Book Sizes
 * All dimensions in inches for print accuracy
 */
export const KDP_BOOK_SIZES = {
  // Mass Market Paperback
  MASS_MARKET: {
    name: "Mass Market (4.25\" × 6.87\")",
    width: 4.25,
    height: 6.87,
    category: "mass-market",
    minPages: 75,
    maxPages: 828,
  },
  
  // Trade Paperback - Most Popular
  TRADE_SMALL: {
    name: "Trade Small (5\" × 8\")",
    width: 5,
    height: 8,
    category: "trade",
    minPages: 24,
    maxPages: 828,
    popular: true,
  },
  
  TRADE_MEDIUM: {
    name: "Trade Medium (5.25\" × 8\")",
    width: 5.25,
    height: 8,
    category: "trade",
    minPages: 24,
    maxPages: 828,
  },
  
  TRADE_STANDARD: {
    name: "Trade Standard (5.5\" × 8.5\")",
    width: 5.5,
    height: 8.5,
    category: "trade",
    minPages: 24,
    maxPages: 828,
  },
  
  TRADE_LARGE: {
    name: "Trade Large (6\" × 9\")",
    width: 6,
    height: 9,
    category: "trade",
    minPages: 24,
    maxPages: 828,
    popular: true,
  },
  
  // Comic and Graphic Novel
  COMIC_STANDARD: {
    name: "Comic Standard (6.625\" × 10.25\")",
    width: 6.625,
    height: 10.25,
    category: "comic",
    minPages: 1,
    maxPages: 300,
  },
  
  // Royal Format
  ROYAL: {
    name: "Royal (6.14\" × 9.21\")",
    width: 6.14,
    height: 9.21,
    category: "royal",
    minPages: 24,
    maxPages: 828,
  },
  
  // Crown Format
  CROWN: {
    name: "Crown (7.44\" × 9.69\")",
    width: 7.44,
    height: 9.69,
    category: "crown",
    minPages: 24,
    maxPages: 828,
  },
  
  // Large Format
  LARGE_FORMAT: {
    name: "Large Format (7\" × 10\")",
    width: 7,
    height: 10,
    category: "large",
    minPages: 24,
    maxPages: 600,
  },
  
  // Square Formats
  SQUARE_SMALL: {
    name: "Square Small (7\" × 7\")",
    width: 7,
    height: 7,
    category: "square",
    minPages: 24,
    maxPages: 480,
  },
  
  SQUARE_LARGE: {
    name: "Square Large (8\" × 8\")",
    width: 8,
    height: 8,
    category: "square",
    minPages: 24,
    maxPages: 480,
  },
  
  // Landscape
  LANDSCAPE: {
    name: "Landscape (10\" × 8\")",
    width: 10,
    height: 8,
    category: "landscape",
    minPages: 24,
    maxPages: 420,
  },
  
  // US Letter
  US_LETTER: {
    name: "US Letter (8.5\" × 11\")",
    width: 8.5,
    height: 11,
    category: "letter",
    minPages: 24,
    maxPages: 828,
    popular: true,
  },
  
  // A4 International
  A4: {
    name: "A4 (8.27\" × 11.69\")",
    width: 8.27,
    height: 11.69,
    category: "international",
    minPages: 24,
    maxPages: 828,
  },
} as const;

/**
 * Print Resolution Settings
 * KDP requires 300 DPI for professional print quality
 */
export const PRINT_RESOLUTION = {
  DPI: 300,
  WEB_DPI: 72, // For preview/web display
  PIXELS_PER_INCH: 300,
  
  // Calculate pixel dimensions from inches
  inchesToPixels: (inches: number, dpi: number = 300) => Math.round(inches * dpi),
  pixelsToInches: (pixels: number, dpi: number = 300) => pixels / dpi,
} as const;

/**
 * Print Margins and Bleeds
 * KDP-specific margin requirements for professional printing
 */
export const PRINT_MARGINS = {
  // Standard margins in inches
  STANDARD: {
    top: 0.75,
    bottom: 0.75,
    inside: 0.75,
    outside: 0.75,
  },
  
  // Minimum margins for KDP
  MINIMUM: {
    top: 0.25,
    bottom: 0.25,
    inside: 0.25,
    outside: 0.25,
  },
  
  // Recommended margins for readability
  RECOMMENDED: {
    top: 1,
    bottom: 1,
    inside: 1,
    outside: 0.75,
  },
  
  // Bleed settings (extends beyond trim)
  BLEED: {
    top: 0.125,
    bottom: 0.125,
    left: 0.125,
    right: 0.125,
  },
  
  // Safe area (content should stay within)
  SAFE_AREA: {
    top: 0.5,
    bottom: 0.5,
    inside: 0.5,
    outside: 0.5,
  },
} as const;

/**
 * CMYK Color Profile for Print
 * Professional printing color space
 */
export const CMYK_COLORS = {
  // Primary colors
  BLACK: { c: 0, m: 0, y: 0, k: 100 },
  WHITE: { c: 0, m: 0, y: 0, k: 0 },
  
  // Rich black for text
  RICH_BLACK: { c: 30, m: 30, y: 30, k: 100 },
  
  // Gray scale
  GRAY_10: { c: 0, m: 0, y: 0, k: 10 },
  GRAY_25: { c: 0, m: 0, y: 0, k: 25 },
  GRAY_50: { c: 0, m: 0, y: 0, k: 50 },
  GRAY_75: { c: 0, m: 0, y: 0, k: 75 },
  
  // Convert RGB to CMYK approximation
  rgbToCmyk: (r: number, g: number, b: number) => {
    const rPercent = r / 255;
    const gPercent = g / 255;
    const bPercent = b / 255;
    
    const k = 1 - Math.max(rPercent, gPercent, bPercent);
    const c = (1 - rPercent - k) / (1 - k) || 0;
    const m = (1 - gPercent - k) / (1 - k) || 0;
    const y = (1 - bPercent - k) / (1 - k) || 0;
    
    return {
      c: Math.round(c * 100),
      m: Math.round(m * 100),
      y: Math.round(y * 100),
      k: Math.round(k * 100),
    };
  },
  
  // Convert CMYK to RGB approximation
  cmykToRgb: (c: number, m: number, y: number, k: number) => {
    const r = 255 * (1 - c / 100) * (1 - k / 100);
    const g = 255 * (1 - m / 100) * (1 - k / 100);
    const b = 255 * (1 - y / 100) * (1 - k / 100);
    
    return {
      r: Math.round(r),
      g: Math.round(g),
      b: Math.round(b),
    };
  },
} as const;

/**
 * Typography Settings for Print
 * Professional typography specifications for book publishing
 */
export const TYPOGRAPHY = {
  // Recommended font sizes in points
  FONT_SIZES: {
    TITLE: { min: 18, max: 36, recommended: 24 },
    CHAPTER_HEADING: { min: 14, max: 24, recommended: 18 },
    BODY_TEXT: { min: 9, max: 12, recommended: 11 },
    FOOTNOTE: { min: 8, max: 10, recommended: 9 },
    CAPTION: { min: 8, max: 10, recommended: 9 },
  },
  
  // Line spacing (leading) in points
  LINE_SPACING: {
    SINGLE: 1,
    ONE_AND_HALF: 1.5,
    DOUBLE: 2,
    TITLE: 1.2,
    BODY: 1.4,
    FOOTNOTE: 1.2,
  },
  
  // Paragraph spacing
  PARAGRAPH_SPACING: {
    BEFORE: 0,
    AFTER: 6, // 6 points after paragraphs
    INDENT: 18, // 18 points first line indent
  },
  
  // KDP-approved fonts for embedding
  APPROVED_FONTS: [
    'Times New Roman',
    'Georgia',
    'Garamond',
    'Minion Pro',
    'Adobe Caslon Pro',
    'Book Antiqua',
    'Palatino',
    'Century',
    'Baskerville',
    'Libre Baskerville',
    'EB Garamond',
    'Crimson Text',
    'Lora',
    'Merriweather',
    'PT Serif',
    'Source Serif Pro',
  ],
  
  // Font categories
  FONT_CATEGORIES: {
    SERIF: 'serif',
    SANS_SERIF: 'sans-serif',
    MONOSPACE: 'monospace',
    DECORATIVE: 'decorative',
  },
} as const;

/**
 * PDF Export Settings
 * KDP-compliant PDF generation settings
 */
export const PDF_SETTINGS = {
  // PDF/A compliance for archival quality
  VERSION: 'PDF/A-1b',
  
  // Color profile
  COLOR_PROFILE: 'sRGB',
  
  // Compression settings
  COMPRESSION: {
    IMAGES: 'jpeg',
    QUALITY: 85,
    DPI: 300,
  },
  
  // Font embedding
  FONT_EMBEDDING: {
    EMBED_ALL: true,
    SUBSET: true,
    OUTLINE_FONTS: false,
  },
  
  // Page settings
  PAGE: {
    ORIENTATION: 'portrait',
    COLOR_SPACE: 'rgb',
    BLEED_MARKS: false,
    CROP_MARKS: false,
  },
  
  // Security settings
  SECURITY: {
    PRINTING: 'highResolution',
    COPYING: false,
    CONTENT_ACCESSIBILITY: true,
    DOCUMENT_ASSEMBLY: false,
  },
} as const;

/**
 * Canvas Configuration for Print
 * Fabric.js settings optimized for print layout
 */
export const PRINT_CANVAS_CONFIG = {
  // Default background for print
  backgroundColor: '#ffffff',
  
  // High-resolution rendering
  enableRetinaScaling: true,
  renderOnAddRemove: true,
  
  // Print-specific selection styles
  selectionColor: 'rgba(100, 100, 255, 0.3)',
  selectionBorderColor: 'rgba(100, 100, 255, 0.8)',
  selectionLineWidth: 2,
  
  // Grid settings for layout
  gridSize: 12, // 12-point grid system
  snapToGrid: true,
  snapAngle: 15, // Snap rotation to 15-degree intervals
  
  // Performance settings for high-res
  perfLimitSizeTotal: 16777216, // 16MB limit for print resolution
  maxCacheSideLimit: 4096,
  minCacheSideLimit: 256,
} as const;

/**
 * Page Template Defaults
 * Standard page layouts for different book types
 */
export const PAGE_TEMPLATES = {
  BLANK: {
    name: 'Blank Page',
    type: 'blank',
    objects: [],
  },
  
  TITLE_PAGE: {
    name: 'Title Page',
    type: 'title',
    objects: [
      {
        type: 'textbox',
        content: 'Book Title',
        style: 'title',
        position: { x: '50%', y: '30%' },
      },
      {
        type: 'textbox',
        content: 'Author Name',
        style: 'author',
        position: { x: '50%', y: '70%' },
      },
    ],
  },
  
  CHAPTER_START: {
    name: 'Chapter Start',
    type: 'chapter',
    objects: [
      {
        type: 'textbox',
        content: 'Chapter 1',
        style: 'chapter',
        position: { x: '50%', y: '20%' },
      },
    ],
  },
  
  STANDARD_TEXT: {
    name: 'Standard Text',
    type: 'text',
    objects: [
      {
        type: 'textbox',
        content: 'Page content goes here...',
        style: 'body',
        position: { x: '50%', y: '50%' },
      },
    ],
  },
} as const;

/**
 * Validation Rules
 * KDP compliance validation
 */
export const VALIDATION_RULES = {
  // File size limits
  MAX_FILE_SIZE_MB: 650,
  
  // Image resolution requirements
  MIN_IMAGE_DPI: 300,
  MAX_IMAGE_DPI: 600,
  
  // Color mode validation
  ALLOWED_COLOR_MODES: ['RGB', 'CMYK', 'Grayscale'],
  
  // Font validation
  validateFont: (fontFamily: string) => {
    return TYPOGRAPHY.APPROVED_FONTS.includes(fontFamily);
  },
  
  // Page count validation
  validatePageCount: (pages: number, bookSize: keyof typeof KDP_BOOK_SIZES) => {
    const size = KDP_BOOK_SIZES[bookSize];
    return pages >= size.minPages && pages <= size.maxPages;
  },
  
  // Margin validation
  validateMargins: (margins: any, bookSize: keyof typeof KDP_BOOK_SIZES) => {
    const min = PRINT_MARGINS.MINIMUM;
    return (
      margins.top >= min.top &&
      margins.bottom >= min.bottom &&
      margins.inside >= min.inside &&
      margins.outside >= min.outside
    );
  },
} as const;

// Helper functions
export const getBookSizePixels = (bookSize: keyof typeof KDP_BOOK_SIZES, dpi: number = 300) => {
  const size = KDP_BOOK_SIZES[bookSize];
  if (!size) {
    console.error(`Invalid book size: ${bookSize}. Using TRADE_LARGE as default.`);
    const defaultSize = KDP_BOOK_SIZES.TRADE_LARGE;
    return {
      width: PRINT_RESOLUTION.inchesToPixels(defaultSize.width, dpi),
      height: PRINT_RESOLUTION.inchesToPixels(defaultSize.height, dpi),
    };
  }
  return {
    width: PRINT_RESOLUTION.inchesToPixels(size.width, dpi),
    height: PRINT_RESOLUTION.inchesToPixels(size.height, dpi),
  };
};

export const getPopularBookSizes = () => {
  return Object.entries(KDP_BOOK_SIZES)
    .filter(([_, size]) => size.popular)
    .map(([key, size]) => ({ key, ...size }));
};

export const getBookSizesByCategory = (category: string) => {
  return Object.entries(KDP_BOOK_SIZES)
    .filter(([_, size]) => size.category === category)
    .map(([key, size]) => ({ key, ...size }));
};
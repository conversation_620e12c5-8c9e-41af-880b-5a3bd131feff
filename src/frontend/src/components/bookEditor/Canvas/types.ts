import * as fabric from "fabric";
import type { HTMLAttributes } from "react";
import { KDP_BOOK_SIZES, PRINT_MARGINS } from "./constants";

/**
 * Book Canvas Types - BE-001 Implementation
 * 
 * Type definitions for BookCanvas component extending PhotoCanvas
 * with print-optimized features for Amazon KDP book creation.
 * Follows LEVER principles by extending existing PhotoCanvas types
 * and adding book-specific functionality.
 */

/**
 * Book Page Interface
 * Represents individual pages in a multi-page book
 */
export interface BookPage {
  /** Unique page identifier */
  id: string;
  
  /** Page number (1-based) */
  pageNumber: number;
  
  /** Page type (title, chapter, content, etc.) */
  pageType: 'title' | 'chapter' | 'content' | 'blank' | 'toc' | 'index';
  
  /** Page name/title for navigation */
  name: string;
  
  /** Fabric.js canvas state for this page */
  canvasState: string; // JSON serialized canvas
  
  /** Page-specific settings */
  settings: {
    /** Margins for this page */
    margins: typeof PRINT_MARGINS.STANDARD;
    
    /** Background color */
    backgroundColor: string;
    
    /** Master page template ID (if any) */
    masterPageId?: string;
    
    /** Whether page is locked from editing */
    locked: boolean;
    
    /** Page visibility in export */
    visible: boolean;
  };
  
  /** Page metadata */
  metadata: {
    /** Created timestamp */
    createdAt: string;
    
    /** Last modified timestamp */
    modifiedAt: string;
    
    /** Page notes/comments */
    notes?: string;
    
    /** Custom properties */
    customProperties?: Record<string, any>;
  };
}

/**
 * Book Canvas State Interface
 * Extends CanvasState with book-specific properties
 */
export interface BookCanvasState {
  /** Canvas dimensions in pixels at 300 DPI */
  width: number;
  height: number;
  
  /** Current book size configuration */
  bookSize: keyof typeof KDP_BOOK_SIZES;
  
  /** Print resolution (always 300 DPI for books) */
  dpi: number;
  
  /** All pages in the book */
  pages: BookPage[];
  
  /** Currently active page index */
  currentPageIndex: number;
  
  /** Canvas transformation */
  zoom: number;
  panX: number;
  panY: number;
  
  /** Multi-page history management */
  historyIndex: number;
  canUndo: boolean;
  canRedo: boolean;
  
  /** Book-level settings */
  bookSettings: {
    /** Global margins */
    margins: typeof PRINT_MARGINS.STANDARD;
    
    /** Default font settings */
    defaultFont: {
      family: string;
      size: number;
      lineHeight: number;
      color: string;
    };
    
    /** Color mode for printing */
    colorMode: 'RGB' | 'CMYK';
    
    /** Bleed settings */
    bleed: typeof PRINT_MARGINS.BLEED;
    
    /** Grid and guides */
    showGrid: boolean;
    showMargins: boolean;
    showBleed: boolean;
    snapToGrid: boolean;
    gridSize: number;
  };
  
  /** Performance metrics */
  performance: {
    totalPages: number;
    currentPageObjectCount: number;
    lastRenderTime: number;
    memoryUsage: number;
  };
}

/**
 * Master Page Template Interface
 * Reusable page templates for consistency
 */
export interface MasterPageTemplate {
  /** Template identifier */
  id: string;
  
  /** Template name */
  name: string;
  
  /** Template category */
  category: 'header' | 'footer' | 'layout' | 'decoration';
  
  /** Template description */
  description: string;
  
  /** Fabric.js objects that make up the template */
  objects: fabric.Object[];
  
  /** Template settings */
  settings: {
    /** Whether objects are locked */
    locked: boolean;
    
    /** Layer order */
    zIndex: number;
    
    /** Template visibility */
    visible: boolean;
  };
  
  /** Template metadata */
  metadata: {
    createdAt: string;
    modifiedAt: string;
    thumbnail?: string;
  };
}

/**
 * Book Canvas Configuration
 * Extends PhotoCanvas config with book-specific settings
 */
export interface BookCanvasConfig {
  /** Book size selection */
  bookSize: keyof typeof KDP_BOOK_SIZES;
  
  /** Print resolution (fixed at 300 DPI) */
  dpi?: number;
  
  /** Initial page count */
  initialPageCount?: number;
  
  /** Default margins */
  margins?: typeof PRINT_MARGINS.STANDARD;
  
  /** Background settings */
  backgroundColor?: string;
  backgroundImage?: string;
  
  /** Grid and guide settings */
  showGrid?: boolean;
  showMargins?: boolean;
  showBleed?: boolean;
  gridSize?: number;
  gridColor?: string;
  snapToGrid?: boolean;
  
  /** Typography defaults */
  defaultFont?: {
    family: string;
    size: number;
    lineHeight: number;
    color: string;
  };
  
  /** Color mode for print */
  colorMode?: 'RGB' | 'CMYK';
  
  /** Interaction settings */
  enableZoom?: boolean;
  enablePan?: boolean;
  minZoom?: number;
  maxZoom?: number;
  
  /** Multi-page settings */
  enablePageNavigation?: boolean;
  showPageThumbnails?: boolean;
  maxPages?: number;
  
  /** Performance settings */
  renderOnAddRemove?: boolean;
  stateful?: boolean;
  enableRetinaScaling?: boolean;
  
  /** Custom properties */
  customProperties?: Record<string, any>;
}

/**
 * Book Data Interface
 * Complete book project data structure
 */
export interface BookData {
  /** Book metadata */
  metadata: {
    title: string;
    author: string;
    isbn?: string;
    publisher?: string;
    publicationDate?: string;
    genre?: string;
    description?: string;
    keywords?: string[];
  };
  
  /** Book configuration */
  config: BookCanvasConfig;
  
  /** All pages in the book */
  pages: BookPage[];
  
  /** Master page templates */
  masterPages: MasterPageTemplate[];
  
  /** Project settings */
  project: {
    version: string;
    createdAt: string;
    modifiedAt: string;
    lastSavedAt?: string;
    fileSize?: number;
  };
}

/**
 * Performance Metrics for Book Canvas
 * Extended performance tracking for multi-page documents
 */
export interface BookPerformanceMetrics {
  /** Page-specific metrics */
  currentPageProcessingTime: number;
  currentPageMemoryUsage: number;
  currentPageRenderTime: number;
  currentPageObjectCount: number;
  
  /** Book-level metrics */
  totalPages: number;
  totalObjects: number;
  averagePageSize: number;
  largestPageSize: number;
  
  /** System metrics */
  memoryUsage: number;
  renderTime: number;
  lastUpdate: number;
  
  /** Export metrics */
  lastExportTime?: number;
  lastExportSize?: number;
}

/**
 * Book Canvas Event Handlers
 * Extended event handlers for book-specific operations
 */
export interface BookCanvasEventHandlers {
  /** Canvas lifecycle events */
  onCanvasReady?: (canvas: fabric.Canvas, state: BookCanvasState) => void;
  onStateChange?: (state: BookCanvasState) => void;
  onError?: (error: string) => void;
  
  /** Page management events */
  onPageChanged?: (pageIndex: number, page: BookPage) => void;
  onPageAdded?: (page: BookPage, index: number) => void;
  onPageRemoved?: (pageId: string, index: number) => void;
  onPageReordered?: (fromIndex: number, toIndex: number) => void;
  onPageDuplicated?: (originalPage: BookPage, newPage: BookPage) => void;
  
  /** Object events (inherited from PhotoCanvas) */
  onObjectAdded?: (object: fabric.Object, pageIndex: number) => void;
  onObjectRemoved?: (object: fabric.Object, pageIndex: number) => void;
  onObjectModified?: (object: fabric.Object, pageIndex: number) => void;
  onSelectionChanged?: (selection: fabric.ActiveSelection | fabric.Object | null) => void;
  
  /** Typography events */
  onTextAdded?: (textObject: fabric.Text) => void;
  onTextModified?: (textObject: fabric.Text) => void;
  onFontChanged?: (fontFamily: string) => void;
  
  /** Master page events */
  onMasterPageApplied?: (masterPageId: string, pageIndex: number) => void;
  onMasterPageCreated?: (masterPage: MasterPageTemplate) => void;
  onMasterPageUpdated?: (masterPage: MasterPageTemplate) => void;
  
  /** Export events */
  onExportStarted?: (format: string) => void;
  onExportProgress?: (progress: number, currentPage: number, totalPages: number) => void;
  onExportCompleted?: (result: BookExportResult) => void;
  onExportFailed?: (error: string) => void;
  
  /** Validation events */
  onValidationStarted?: () => void;
  onValidationCompleted?: (results: ValidationResult[]) => void;
  onValidationFailed?: (errors: ValidationError[]) => void;
}

/**
 * BookCanvas Props Interface
 * Main component props extending PhotoCanvas functionality
 */
export interface BookCanvasProps extends 
  Omit<HTMLAttributes<HTMLDivElement>, keyof BookCanvasEventHandlers>,
  BookCanvasEventHandlers {
  /** Langflow node ID for integration */
  nodeId: string;
  
  /** Book data to load into canvas */
  bookData?: BookData | string;
  
  /** Canvas configuration */
  canvasConfig?: BookCanvasConfig;
  
  /** Canvas dimensions (calculated from book size if not provided) */
  width?: number;
  height?: number;
  
  /** Current page index to display */
  currentPage?: number;
  
  /** Canvas state */
  disabled?: boolean;
  showGrid?: boolean;
  showMargins?: boolean;
  showBleed?: boolean;
  enableZoom?: boolean;
  enablePan?: boolean;
  enablePageNavigation?: boolean;
  
  /** Multi-page settings */
  showPageThumbnails?: boolean;
  thumbnailSize?: number;
  maxPages?: number;
  
  /** Typography settings */
  defaultFont?: string;
  enableTypographyPanel?: boolean;
  
  /** Print preview settings */
  previewMode?: 'edit' | 'print' | 'web';
  showPrintMarks?: boolean;
  
  /** Styling */
  className?: string;
}

/**
 * Book Canvas Hook Return Interface
 * Extended return type for useBookCanvas hook
 */
export interface UseBookCanvasReturn {
  /** Fabric.js canvas instance */
  canvas: fabric.Canvas | null;
  
  /** Current book canvas state */
  canvasState: BookCanvasState;
  
  /** Canvas readiness status */
  isReady: boolean;
  
  /** Error state */
  error: string | null;
  
  /** Performance metrics */
  performance: BookPerformanceMetrics | null;
  
  /** Canvas operations */
  initializeCanvas: () => Promise<void>;
  updateCanvasState: (updates: Partial<BookCanvasState>) => void;
  loadBookData: (bookData: BookData | string) => Promise<void>;
  resetCanvas: () => void;
  
  /** Page management operations */
  currentPage: BookPage | null;
  currentPageIndex: number;
  totalPages: number;
  navigateToPage: (pageIndex: number) => void;
  addPage: (pageType?: BookPage['pageType'], insertAt?: number) => BookPage;
  removePage: (pageIndex: number) => void;
  duplicatePage: (pageIndex: number) => BookPage;
  reorderPage: (fromIndex: number, toIndex: number) => void;
  
  /** Object operations (per page) */
  addObject: (object: fabric.Object, pageIndex?: number) => void;
  removeObject: (object: fabric.Object, pageIndex?: number) => void;
  clearPage: (pageIndex?: number) => void;
  
  /** Typography operations */
  addText: (text: string, options?: fabric.ITextOptions) => fabric.Text;
  updateTextStyle: (textObject: fabric.Text, style: Partial<fabric.ITextOptions>) => void;
  applyFontToSelection: (fontFamily: string) => void;
  
  /** Master page operations */
  masterPages: MasterPageTemplate[];
  createMasterPage: (name: string, objects: fabric.Object[]) => MasterPageTemplate;
  applyMasterPage: (masterPageId: string, pageIndex?: number) => void;
  updateMasterPage: (masterPageId: string, updates: Partial<MasterPageTemplate>) => void;
  removeMasterPage: (masterPageId: string) => void;
  
  /** Selection operations */
  selectObject: (object: fabric.Object) => void;
  clearSelection: () => void;
  getSelectedObjects: () => fabric.Object[];
  
  /** History operations (book-wide) */
  undo: () => void;
  redo: () => void;
  canUndo: boolean;
  canRedo: boolean;
  
  /** Zoom and pan operations */
  zoomIn: () => void;
  zoomOut: () => void;
  resetZoom: () => void;
  fitToPage: () => void;
  centerPage: () => void;
  
  /** Export operations */
  exportPage: (pageIndex: number, format?: string, quality?: number) => string;
  exportBook: (options?: BookExportOptions) => Promise<BookExportResult>;
  exportPDF: (options?: PDFExportOptions) => Promise<Blob>;
  
  /** Validation operations */
  validateBook: () => Promise<ValidationResult[]>;
  validatePage: (pageIndex: number) => ValidationResult[];
  validateKDPCompliance: () => Promise<KDPValidationResult>;
}

/**
 * Book Canvas Hook Options Interface
 * Configuration options for useBookCanvas hook
 */
export interface UseBookCanvasOptions extends BookCanvasConfig, BookCanvasEventHandlers {
  /** Canvas reference */
  canvasRef: React.RefObject<HTMLCanvasElement>;
  
  /** Langflow node ID */
  nodeId: string;
  
  /** Initial book data */
  bookData?: BookData;
  
  /** Performance monitoring */
  trackPerformance?: boolean;
  performanceInterval?: number;
  
  /** Auto-save settings */
  autoSave?: boolean;
  autoSaveInterval?: number;
}

/**
 * Export Options and Results
 */
export interface BookExportOptions {
  format: 'pdf' | 'png' | 'jpg' | 'svg';
  quality?: number;
  dpi?: number;
  includeBleed?: boolean;
  includeMarks?: boolean;
  colorProfile?: 'sRGB' | 'CMYK';
  compression?: boolean;
  
  /** Page range */
  pageRange?: {
    start: number;
    end: number;
  } | 'all';
  
  /** PDF-specific options */
  pdfOptions?: PDFExportOptions;
}

export interface PDFExportOptions {
  version: 'PDF/A-1b' | 'PDF/X-1a' | 'PDF-1.4';
  embedFonts: boolean;
  subsetFonts: boolean;
  colorSpace: 'RGB' | 'CMYK';
  compression: {
    images: 'jpeg' | 'lzw' | 'zip';
    quality: number;
  };
  security?: {
    printing: boolean;
    copying: boolean;
    contentAccessibility: boolean;
  };
}

export interface BookExportResult {
  data: Blob | string;
  format: string;
  pageCount: number;
  size: {
    width: number;
    height: number;
  };
  metadata: {
    timestamp: string;
    totalSize: number;
    processingTime: number;
    pages: Array<{
      pageNumber: number;
      objectCount: number;
      size: number;
    }>;
  };
}

/**
 * Validation Types
 */
export interface ValidationResult {
  type: 'error' | 'warning' | 'info';
  code: string;
  message: string;
  pageIndex?: number;
  objectId?: string;
  suggestion?: string;
}

export interface ValidationError extends ValidationResult {
  type: 'error';
  severity: 'critical' | 'high' | 'medium' | 'low';
}

export interface KDPValidationResult {
  isCompliant: boolean;
  errors: ValidationError[];
  warnings: ValidationResult[];
  summary: {
    totalIssues: number;
    criticalIssues: number;
    pageCount: number;
    fileSize: number;
    estimatedPrintCost?: number;
  };
}

/**
 * Langflow Integration Types for Book Canvas
 */
export interface LangflowBookCanvasState {
  /** Node identifier */
  nodeId: string;
  
  /** Complete book canvas state */
  bookCanvasState: BookCanvasState;
  
  /** Page-specific edit history */
  editHistory: Array<{
    action: string;
    timestamp: string;
    pageIndex: number;
    changes: any;
    canvasState?: Partial<BookCanvasState>;
  }>;
  
  /** Performance metrics for backend monitoring */
  performance: BookPerformanceMetrics;
  
  /** Component metadata */
  metadata: {
    version: string;
    lastUpdate: string;
    componentType: "BookCanvas";
    bookSize: keyof typeof KDP_BOOK_SIZES;
    pageCount: number;
  };
}

/**
 * Tool Integration Types for Book Canvas
 */
export interface BookCanvasTool {
  id: string;
  name: string;
  icon: string;
  category: "text" | "image" | "shape" | "page" | "master" | "export" | "validation";
  active: boolean;
  bookSpecific: boolean; // Tools that are book-specific vs inherited from PhotoCanvas
  options?: Record<string, any>;
  apply: (canvas: fabric.Canvas, options?: any, pageIndex?: number) => Promise<void>;
}
/**
 * BE-008 Book Tools Configuration
 * 
 * Comprehensive tool definitions for the BookToolPalette.
 * Adapted from PhotoEditor ToolPalette with typography and layout focus.
 * 
 * LEVER Principles:
 * - Leverages ToolPalette structure and patterns
 * - Extends with book-specific tools and parameters
 * - Verifies professional publishing requirements
 * - Eliminates duplication through smart categorization
 * - Reduces complexity with intuitive tool organization
 */

import {
  Type,
  AlignLeft,
  AlignCenter,
  AlignRight,
  AlignJustify,
  Bold,
  Italic,
  Underline,
  Palette,
  Columns,
  Square,
  Image,
  Table,
  List,
  Hash,
  Indent,
  Outdent,
  AlignVerticalSpaceAround,
  FileText,
  BookOpen,
  Layout,
  Grid3X3,
  Layers,
  Settings,
  Download,
  FileCheck,
  Printer,
  Eye,
  Zap,
  Search,
  Move,
  RotateCcw,
  Ruler,
  Focus,
  PlusCircle,
  Copy,
  Scissors,
  Navigation,
  ChevronLeft,
  ChevronRight,
  Home,
  Bookmark,
  Heading,
  ArrowDown as Footer,
  ArrowUp as Header,
  Separator as PageBreak,
  SeparatorHorizontal as Spacing,
  Type as FontSizeIcon,
  ArrowDown,
  Separator,
  Sparkles,
  Brain,
  PenTool,
  MessageSquare,
  CheckCircle,
  SeparatorHorizontal,
} from 'lucide-react';

import type { 
  BookToolDefinition, 
  BookToolCategory, 
  FontDefinition 
} from './types';

// Web-safe fonts for book publishing
export const WEB_SAFE_FONTS: FontDefinition[] = [
  {
    family: 'Georgia',
    name: 'Georgia',
    category: 'serif',
    weights: [400, 700],
    styles: ['normal', 'italic'],
    isWebSafe: true
  },
  {
    family: 'Times New Roman',
    name: 'Times New Roman',
    category: 'serif',
    weights: [400, 700],
    styles: ['normal', 'italic'],
    isWebSafe: true
  },
  {
    family: 'Arial',
    name: 'Arial',
    category: 'sans-serif',
    weights: [400, 700],
    styles: ['normal', 'italic'],
    isWebSafe: true
  },
  {
    family: 'Helvetica',
    name: 'Helvetica',
    category: 'sans-serif',
    weights: [400, 700],
    styles: ['normal', 'italic'],
    isWebSafe: true
  },
  {
    family: 'Courier New',
    name: 'Courier New',
    category: 'monospace',
    weights: [400, 700],
    styles: ['normal', 'italic'],
    isWebSafe: true
  }
];

// Google Fonts optimized for book publishing
export const GOOGLE_FONTS: FontDefinition[] = [
  {
    family: 'Crimson Text',
    name: 'Crimson Text',
    category: 'serif',
    weights: [400, 600, 700],
    styles: ['normal', 'italic'],
    isGoogleFont: true
  },
  {
    family: 'Libre Baskerville',
    name: 'Libre Baskerville',
    category: 'serif',
    weights: [400, 700],
    styles: ['normal', 'italic'],
    isGoogleFont: true
  },
  {
    family: 'Open Sans',
    name: 'Open Sans',
    category: 'sans-serif',
    weights: [300, 400, 600, 700],
    styles: ['normal', 'italic'],
    isGoogleFont: true
  },
  {
    family: 'Lora',
    name: 'Lora',
    category: 'serif',
    weights: [400, 500, 600, 700],
    styles: ['normal', 'italic'],
    isGoogleFont: true
  }
];

// Typography Tools - Replace image filters
const TYPOGRAPHY_TOOLS: BookToolDefinition[] = [
  {
    id: 'text_create',
    name: 'Create Text',
    category: 'typography',
    icon: Type,
    description: 'Create new text boxes and content',
    shortcut: 'T',
    usage: 'frequent',
    parameters: [
      {
        name: 'font_family',
        type: 'select',
        defaultValue: 'Georgia',
        options: WEB_SAFE_FONTS.map(font => ({ 
          value: font.family, 
          label: font.name 
        })),
        description: 'Choose font family'
      },
      {
        name: 'font_size',
        type: 'slider',
        min: 8,
        max: 72,
        step: 1,
        defaultValue: 12,
        unit: 'pt',
        description: 'Font size in points'
      },
      {
        name: 'line_height',
        type: 'slider',
        min: 0.8,
        max: 3.0,
        step: 0.1,
        defaultValue: 1.4,
        description: 'Line spacing multiplier'
      }
    ]
  },
  {
    id: 'font_selector',
    name: 'Font Family',
    category: 'typography',
    icon: FontSizeIcon,
    description: 'Change font family and style',
    shortcut: 'Ctrl+Shift+F',
    usage: 'frequent',
    parameters: [
      {
        name: 'font_family',
        type: 'font',
        defaultValue: 'Georgia',
        fontOptions: [...WEB_SAFE_FONTS, ...GOOGLE_FONTS],
        description: 'Select font family'
      },
      {
        name: 'font_weight',
        type: 'select',
        defaultValue: '400',
        options: [
          { value: '300', label: 'Light' },
          { value: '400', label: 'Regular' },
          { value: '600', label: 'Semi-Bold' },
          { value: '700', label: 'Bold' }
        ],
        description: 'Font weight'
      }
    ]
  },
  {
    id: 'text_align_left',
    name: 'Align Left',
    category: 'typography',
    icon: AlignLeft,
    description: 'Align text to the left',
    shortcut: 'Ctrl+L',
    usage: 'frequent'
  },
  {
    id: 'text_align_center',
    name: 'Align Center',
    category: 'typography',
    icon: AlignCenter,
    description: 'Center align text',
    shortcut: 'Ctrl+E',
    usage: 'frequent'
  },
  {
    id: 'text_align_right',
    name: 'Align Right',
    category: 'typography',
    icon: AlignRight,
    description: 'Align text to the right',
    shortcut: 'Ctrl+R',
    usage: 'frequent'
  },
  {
    id: 'text_align_justify',
    name: 'Justify',
    category: 'typography',
    icon: AlignJustify,
    description: 'Justify text alignment',
    shortcut: 'Ctrl+J',
    usage: 'frequent'
  },
  {
    id: 'text_bold',
    name: 'Bold',
    category: 'typography',
    icon: Bold,
    description: 'Make text bold',
    shortcut: 'Ctrl+B',
    usage: 'frequent'
  },
  {
    id: 'text_italic',
    name: 'Italic',
    category: 'typography',
    icon: Italic,
    description: 'Make text italic',
    shortcut: 'Ctrl+I',
    usage: 'frequent'
  },
  {
    id: 'text_underline',
    name: 'Underline',
    category: 'typography',
    icon: Underline,
    description: 'Underline text',
    shortcut: 'Ctrl+U',
    usage: 'moderate'
  },
  {
    id: 'line_spacing',
    name: 'Line Spacing',
    category: 'typography',
    icon: AlignVerticalSpaceAround,
    description: 'Adjust line height and spacing',
    shortcut: 'Ctrl+Shift+L',
    usage: 'frequent',
    parameters: [
      {
        name: 'line_height',
        type: 'slider',
        min: 0.8,
        max: 3.0,
        step: 0.1,
        defaultValue: 1.4,
        description: 'Line height multiplier'
      },
      {
        name: 'paragraph_spacing',
        type: 'slider',
        min: 0,
        max: 24,
        step: 1,
        defaultValue: 6,
        unit: 'pt',
        description: 'Space after paragraph'
      }
    ]
  },
  {
    id: 'drop_cap',
    name: 'Drop Cap',
    category: 'typography',
    icon: Type,
    description: 'Create decorative drop capitals',
    usage: 'occasional',
    parameters: [
      {
        name: 'lines',
        type: 'slider',
        min: 2,
        max: 6,
        step: 1,
        defaultValue: 3,
        description: 'Number of lines to span'
      },
      {
        name: 'style',
        type: 'select',
        defaultValue: 'raised',
        options: [
          { value: 'raised', label: 'Raised' },
          { value: 'sunken', label: 'Sunken' },
          { value: 'shadow', label: 'Drop Shadow' }
        ],
        description: 'Drop cap style'
      }
    ]
  }
];

// Layout Tools - Professional page layout
const LAYOUT_TOOLS: BookToolDefinition[] = [
  {
    id: 'text_frame',
    name: 'Text Frame',
    category: 'layout',
    icon: Square,
    description: 'Create text frames and containers',
    shortcut: 'F',
    usage: 'frequent',
    parameters: [
      {
        name: 'columns',
        type: 'slider',
        min: 1,
        max: 4,
        step: 1,
        defaultValue: 1,
        description: 'Number of columns'
      },
      {
        name: 'column_gap',
        type: 'slider',
        min: 6,
        max: 36,
        step: 2,
        defaultValue: 12,
        unit: 'pt',
        description: 'Gap between columns'
      },
      {
        name: 'padding',
        type: 'slider',
        min: 0,
        max: 24,
        step: 2,
        defaultValue: 6,
        unit: 'pt',
        description: 'Internal padding'
      }
    ]
  },
  {
    id: 'columns',
    name: 'Column Layout',
    category: 'layout',
    icon: Columns,
    description: 'Create multi-column text layouts',
    shortcut: 'Ctrl+Shift+C',
    usage: 'frequent',
    parameters: [
      {
        name: 'column_count',
        type: 'slider',
        min: 1,
        max: 4,
        step: 1,
        defaultValue: 2,
        description: 'Number of columns'
      },
      {
        name: 'column_gap',
        type: 'slider',
        min: 6,
        max: 36,
        step: 2,
        defaultValue: 18,
        unit: 'pt',
        description: 'Space between columns'
      },
      {
        name: 'balance_columns',
        type: 'toggle',
        defaultValue: true,
        description: 'Balance column heights'
      }
    ]
  },
  {
    id: 'image_placement',
    name: 'Image Frame',
    category: 'layout',
    icon: Image,
    description: 'Place and position images',
    shortcut: 'Ctrl+Shift+I',
    usage: 'moderate',
    parameters: [
      {
        name: 'wrap_style',
        type: 'select',
        defaultValue: 'square',
        options: [
          { value: 'none', label: 'No Wrap' },
          { value: 'square', label: 'Square Wrap' },
          { value: 'tight', label: 'Tight Wrap' },
          { value: 'through', label: 'Through Wrap' }
        ],
        description: 'Text wrapping style'
      },
      {
        name: 'wrap_margin',
        type: 'slider',
        min: 0,
        max: 18,
        step: 1,
        defaultValue: 6,
        unit: 'pt',
        description: 'Text wrap margin'
      }
    ]
  },
  {
    id: 'table_create',
    name: 'Table',
    category: 'layout',
    icon: Table,
    description: 'Create and format tables',
    shortcut: 'Ctrl+Shift+T',
    usage: 'moderate',
    parameters: [
      {
        name: 'rows',
        type: 'slider',
        min: 1,
        max: 20,
        step: 1,
        defaultValue: 3,
        description: 'Number of rows'
      },
      {
        name: 'columns',
        type: 'slider',
        min: 1,
        max: 8,
        step: 1,
        defaultValue: 3,
        description: 'Number of columns'
      },
      {
        name: 'header_row',
        type: 'toggle',
        defaultValue: true,
        description: 'Include header row'
      }
    ]
  },
  {
    id: 'list_create',
    name: 'Lists',
    category: 'layout',
    icon: List,
    description: 'Create bulleted and numbered lists',
    shortcut: 'Ctrl+Shift+L',
    usage: 'frequent',
    parameters: [
      {
        name: 'list_type',
        type: 'select',
        defaultValue: 'bullet',
        options: [
          { value: 'bullet', label: 'Bullet Points' },
          { value: 'number', label: 'Numbers' },
          { value: 'letter', label: 'Letters' },
          { value: 'roman', label: 'Roman Numerals' }
        ],
        description: 'List style type'
      },
      {
        name: 'indent',
        type: 'slider',
        min: 6,
        max: 36,
        step: 2,
        defaultValue: 18,
        unit: 'pt',
        description: 'List indentation'
      }
    ]
  },
  {
    id: 'indent_increase',
    name: 'Increase Indent',
    category: 'layout',
    icon: Indent,
    description: 'Increase paragraph indentation',
    shortcut: 'Tab',
    usage: 'frequent'
  },
  {
    id: 'indent_decrease',
    name: 'Decrease Indent',
    category: 'layout',
    icon: Outdent,
    description: 'Decrease paragraph indentation',
    shortcut: 'Shift+Tab',
    usage: 'frequent'
  }
];

// Page Management Tools
const PAGE_TOOLS: BookToolDefinition[] = [
  {
    id: 'page_navigation',
    name: 'Page Navigator',
    category: 'pages',
    icon: Navigation,
    description: 'Navigate between pages',
    shortcut: 'Ctrl+G',
    usage: 'frequent',
    parameters: [
      {
        name: 'page_number',
        type: 'number',
        min: 1,
        max: 999,
        defaultValue: 1,
        description: 'Go to page number'
      }
    ]
  },
  {
    id: 'page_previous',
    name: 'Previous Page',
    category: 'pages',
    icon: ChevronLeft,
    description: 'Go to previous page',
    shortcut: 'Ctrl+Left',
    usage: 'frequent'
  },
  {
    id: 'page_next',
    name: 'Next Page',
    category: 'pages',  
    icon: ChevronRight,
    description: 'Go to next page',
    shortcut: 'Ctrl+Right',
    usage: 'frequent'
  },
  {
    id: 'page_add',
    name: 'Add Page',
    category: 'pages',
    icon: PlusCircle,
    description: 'Add new page to document',
    shortcut: 'Ctrl+Shift+N',
    usage: 'frequent',
    parameters: [
      {
        name: 'template',
        type: 'select',
        defaultValue: 'blank',
        options: [
          { value: 'blank', label: 'Blank Page' },
          { value: 'content', label: 'Content Page' },
          { value: 'chapter', label: 'Chapter Start' },
          { value: 'title', label: 'Title Page' }
        ],
        description: 'Page template'
      },
      {
        name: 'position',
        type: 'select',
        defaultValue: 'after',
        options: [
          { value: 'before', label: 'Before Current' },
          { value: 'after', label: 'After Current' },
          { value: 'end', label: 'At End' }
        ],
        description: 'Insert position'
      }
    ]
  },
  {
    id: 'master_pages',
    name: 'Master Pages',
    category: 'pages',
    icon: Layout,
    description: 'Manage page templates and masters',
    shortcut: 'Ctrl+M',
    usage: 'moderate',
    isPro: true
  },
  {
    id: 'chapter_tools',
    name: 'Chapter Tools',
    category: 'pages',
    icon: BookOpen,
    description: 'Chapter and section management',
    shortcut: 'Ctrl+H',
    usage: 'moderate',
    parameters: [
      {
        name: 'chapter_style',
        type: 'select',
        defaultValue: 'auto',
        options: [
          { value: 'auto', label: 'Auto Chapter' },
          { value: 'new_page', label: 'New Page' },
          { value: 'new_odd', label: 'New Odd Page' },
          { value: 'continuous', label: 'Continuous' }
        ],
        description: 'Chapter break style'
      }
    ]
  },
  {
    id: 'headers_footers',
    name: 'Headers & Footers',
    category: 'pages',
    icon: Header,
    description: 'Manage page headers and footers',
    shortcut: 'Ctrl+Shift+H',
    usage: 'moderate',
    parameters: [
      {
        name: 'header_type',
        type: 'select',
        defaultValue: 'different_odd_even',
        options: [
          { value: 'same', label: 'Same on All Pages' },
          { value: 'different_first', label: 'Different First Page' },
          { value: 'different_odd_even', label: 'Different Odd/Even' }
        ],
        description: 'Header/footer style'
      },
      {
        name: 'include_page_numbers',
        type: 'toggle',
        defaultValue: true,
        description: 'Include page numbers'
      }
    ]
  }
];

// Export and Validation Tools
const EXPORT_TOOLS: BookToolDefinition[] = [
  {
    id: 'pdf_export',
    name: 'Export PDF',
    category: 'export',
    icon: Download,
    description: 'Export book as PDF',
    shortcut: 'Ctrl+E',
    usage: 'frequent',
    parameters: [
      {
        name: 'export_type',
        type: 'select',
        defaultValue: 'kdp_print',
        options: [
          { value: 'screen', label: 'Screen Reading' },
          { value: 'kdp_print', label: 'KDP Print Book' },
          { value: 'professional', label: 'Professional Print' },
          { value: 'ebook', label: 'E-book Compatible' }
        ],
        description: 'Export preset'
      },
      {
        name: 'quality',
        type: 'select',
        defaultValue: 'high',
        options: [
          { value: 'draft', label: 'Draft (Fast)' },
          { value: 'standard', label: 'Standard' },
          { value: 'high', label: 'High Quality' },
          { value: 'print', label: 'Print Ready' }
        ],
        description: 'Export quality'
      },
      {
        name: 'embed_fonts',
        type: 'toggle',
        defaultValue: true,
        description: 'Embed fonts in PDF'
      }
    ]
  },
  {
    id: 'print_preview',
    name: 'Print Preview',
    category: 'export',
    icon: Eye,
    description: 'Preview how book will print',
    shortcut: 'Ctrl+P',
    usage: 'frequent'
  },
  {
    id: 'font_validation',
    name: 'Font Check',
    category: 'export',
    icon: FileCheck,
    description: 'Validate fonts for publishing',
    usage: 'moderate',
    isPro: true
  },
  {
    id: 'color_validation',
    name: 'Color Space',
    category: 'export',
    icon: Palette,
    description: 'Convert colors for print (CMYK)',
    usage: 'moderate',
    isPro: true,
    parameters: [
      {
        name: 'target_space',
        type: 'select',
        defaultValue: 'cmyk',
        options: [
          { value: 'rgb', label: 'RGB (Screen)' },
          { value: 'cmyk', label: 'CMYK (Print)' },
          { value: 'grayscale', label: 'Grayscale' }
        ],
        description: 'Target color space'
      }
    ]
  },
  {
    id: 'kdp_validator',
    name: 'KDP Validator',
    category: 'export',
    icon: Zap,
    description: 'Validate for Kindle Direct Publishing',
    usage: 'frequent',
    isNew: true
  }
];

// Utility Tools - Navigation and helpers
const UTILITY_TOOLS: BookToolDefinition[] = [
  {
    id: 'zoom_tool',
    name: 'Zoom',
    category: 'utility',
    icon: Search,
    description: 'Zoom in and out of pages',
    shortcut: 'Z',
    usage: 'frequent',
    parameters: [
      {
        name: 'zoom_level',
        type: 'slider',
        min: 25,
        max: 500,
        step: 25,
        defaultValue: 100,
        unit: '%',
        description: 'Zoom percentage'
      }
    ]
  },
  {
    id: 'hand_tool',
    name: 'Hand Tool',
    category: 'utility',
    icon: Move,
    description: 'Pan around the page',
    shortcut: 'H',
    usage: 'frequent'
  },
  {
    id: 'guides_toggle',
    name: 'Guides & Grid',
    category: 'utility',
    icon: Grid3X3,
    description: 'Toggle layout guides and grid',
    shortcut: 'Ctrl+;',
    usage: 'moderate',
    parameters: [
      {
        name: 'show_margins',
        type: 'toggle',
        defaultValue: true,
        description: 'Show page margins'
      },
      {
        name: 'show_grid',
        type: 'toggle',
        defaultValue: false,
        description: 'Show layout grid'
      },
      {
        name: 'show_bleeds',
        type: 'toggle',
        defaultValue: false,
        description: 'Show print bleeds'
      }
    ]
  },
  {
    id: 'rulers',
    name: 'Rulers',
    category: 'utility',
    icon: Ruler,
    description: 'Show measurement rulers',
    shortcut: 'Ctrl+R',
    usage: 'occasional'
  },
  {
    id: 'layers_panel',
    name: 'Layers',
    category: 'utility',
    icon: Layers,
    description: 'Manage document layers',
    shortcut: 'F7',
    usage: 'moderate',
    isPro: true
  }
];

// AI Writing Assistant Tools
const AI_TOOLS: BookToolDefinition[] = [
  {
    id: 'ai_writing_assistant',
    name: 'AI Writing Assistant',
    category: 'ai',
    icon: Sparkles,
    description: 'AI-powered writing assistance and content generation',
    shortcut: 'Ctrl+Shift+A',
    usage: 'frequent',
    isNew: true,
    parameters: [
      {
        name: 'operation',
        type: 'select',
        defaultValue: 'generate-content',
        options: [
          { value: 'generate-content', label: 'Generate Content' },
          { value: 'improve-text', label: 'Improve Text' },
          { value: 'check-grammar', label: 'Check Grammar' },
          { value: 'expand-text', label: 'Expand Text' },
          { value: 'summarize', label: 'Summarize' }
        ],
        description: 'AI operation type'
      },
      {
        name: 'provider',
        type: 'select',
        defaultValue: 'auto',
        options: [
          { value: 'auto', label: 'Auto Select' },
          { value: 'turbois', label: 'TurboIs' },
          { value: 'openai', label: 'OpenAI' },
          { value: 'anthropic', label: 'Claude' },
          { value: 'google', label: 'Gemini' }
        ],
        description: 'AI provider preference'
      }
    ]
  },
  {
    id: 'ai_text_improvement',
    name: 'Text Improvement',
    category: 'ai',
    icon: PenTool,
    description: 'Improve selected text clarity and style',
    shortcut: 'Ctrl+Alt+I',
    usage: 'frequent',
    parameters: [
      {
        name: 'improvement_type',
        type: 'select',
        defaultValue: 'general',
        options: [
          { value: 'general', label: 'General Improvement' },
          { value: 'clarity', label: 'Improve Clarity' },
          { value: 'style', label: 'Enhance Style' },
          { value: 'tone', label: 'Adjust Tone' }
        ],
        description: 'Type of improvement'
      }
    ]
  },
  {
    id: 'ai_grammar_check',
    name: 'Grammar & Style Check',
    category: 'ai',
    icon: CheckCircle,
    description: 'Check grammar, spelling, and style issues',
    shortcut: 'F7',
    usage: 'frequent'
  },
  {
    id: 'ai_genre_tools',
    name: 'Genre-Specific Tools',
    category: 'ai',
    icon: Brain,
    description: 'Specialized tools for your book genre',
    shortcut: 'Ctrl+G',
    usage: 'moderate',
    parameters: [
      {
        name: 'genre',
        type: 'select',
        defaultValue: 'fiction',
        options: [
          { value: 'fiction', label: 'Fiction' },
          { value: 'non-fiction', label: 'Non-Fiction' },
          { value: 'academic', label: 'Academic' },
          { value: 'children', label: 'Children\'s Book' },
          { value: 'technical', label: 'Technical' }
        ],
        description: 'Book genre'
      }
    ]
  },
  {
    id: 'ai_research_assistant',
    name: 'Research Assistant',
    category: 'ai',
    icon: Search,
    description: 'AI-powered research and fact-checking',
    shortcut: 'Ctrl+Shift+R',
    usage: 'moderate',
    parameters: [
      {
        name: 'research_type',
        type: 'select',
        defaultValue: 'topic',
        options: [
          { value: 'topic', label: 'Topic Research' },
          { value: 'fact-check', label: 'Fact Checking' },
          { value: 'citations', label: 'Generate Citations' }
        ],
        description: 'Research operation'
      }
    ]
  },
  {
    id: 'ai_dialogue_enhancement',
    name: 'Dialogue Enhancement',
    category: 'ai',
    icon: MessageSquare,
    description: 'Improve dialogue naturalness and character voice',
    usage: 'moderate',
    parameters: [
      {
        name: 'focus',
        type: 'select',
        defaultValue: 'naturalness',
        options: [
          { value: 'naturalness', label: 'Natural Flow' },
          { value: 'character-voice', label: 'Character Voice' },
          { value: 'pacing', label: 'Dialogue Pacing' }
        ],
        description: 'Enhancement focus'
      }
    ]
  }
];

// Combine all tools
export const BOOK_TOOLS: BookToolDefinition[] = [
  ...AI_TOOLS,
  ...TYPOGRAPHY_TOOLS,
  ...LAYOUT_TOOLS,
  ...PAGE_TOOLS,
  ...EXPORT_TOOLS,
  ...UTILITY_TOOLS
];

// Tool categories configuration
export const BOOK_TOOL_CATEGORIES = {
  ai: {
    name: 'AI Assistant',
    icon: Sparkles,
    description: 'AI-powered writing assistance',
    color: 'bg-violet-500'
  },
  typography: {
    name: 'Typography',
    icon: Type,
    description: 'Text creation and formatting',
    color: 'bg-blue-500'
  },
  layout: {
    name: 'Layout',
    icon: Layout,
    description: 'Page layout and structure',
    color: 'bg-green-500'
  },
  pages: {
    name: 'Pages',
    icon: BookOpen,
    description: 'Page and chapter management',
    color: 'bg-purple-500'
  },
  objects: {
    name: 'Objects',
    icon: Image,
    description: 'Images, tables, and shapes',
    color: 'bg-orange-500'
  },
  export: {
    name: 'Export',
    icon: Download,
    description: 'Publishing and validation',
    color: 'bg-pink-500'
  },
  utility: {
    name: 'Utility',
    icon: Settings,
    description: 'Navigation and tools',
    color: 'bg-gray-500'
  }
};

// Common text styles and presets
export const TEXT_STYLE_PRESETS = {
  body: {
    name: 'Body Text',
    style: {
      fontFamily: 'Georgia',
      fontSize: 11,
      fontWeight: 400,
      lineHeight: 1.4,
      textAlign: 'left' as const,
      color: '#000000'
    }
  },
  heading1: {
    name: 'Chapter Title',
    style: {
      fontFamily: 'Georgia',
      fontSize: 24,
      fontWeight: 700,
      lineHeight: 1.2,
      textAlign: 'left' as const,
      color: '#000000'
    }
  },
  heading2: {
    name: 'Section Heading',
    style: {
      fontFamily: 'Georgia',
      fontSize: 16,
      fontWeight: 600,
      lineHeight: 1.3,
      textAlign: 'left' as const,
      color: '#000000'
    }
  },
  quote: {
    name: 'Block Quote',
    style: {
      fontFamily: 'Georgia',
      fontSize: 10,
      fontWeight: 400,
      fontStyle: 'italic',
      lineHeight: 1.5,
      textAlign: 'left' as const,
      color: '#333333'
    }
  }
};
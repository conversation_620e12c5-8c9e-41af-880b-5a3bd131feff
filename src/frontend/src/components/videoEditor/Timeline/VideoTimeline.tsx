// Video Timeline Component - Core multi-track timeline for video editing
// Following LEVER principles by leveraging @xzdarcy/react-timeline-editor

import React, { useCallback, useEffect, useRef, useState } from "react";
import { 
  Timeline, 
  TimelineEngine, 
  TimelineState as LibTimelineState,
  TimelineAction,
  TimelineRow,
  TimelineEffect,
} from "@xzdarcy/react-timeline-editor";
import { cn } from "@/utils/utils";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Label } from "@/components/ui/label";
import ForwardedIconComponent from "@/components/common/genericIconComponent";
import { useVideoEditorStore } from "@/stores/videoEditorStore";
import type { 
  VideoTimelineProps,
  TimelineTrack,
  TimelineClip,
  TrackType,
  PlaybackState,
} from "./types";

// Timeline styles following existing theme patterns
const timelineStyles = {
  timeline: "w-full h-full bg-background border border-border rounded-lg overflow-hidden",
  track: "bg-card border-b border-border min-h-[60px] relative",
  clip: "absolute rounded-md shadow-sm cursor-move border border-border",
  playhead: "absolute top-0 bottom-0 w-0.5 bg-primary z-10 pointer-events-none",
  ruler: "bg-muted border-b border-border h-8 flex items-center px-2 text-xs text-muted-foreground",
  trackHeader: "w-32 bg-card border-r border-border flex flex-col justify-center px-3 text-sm",
};

// Convert our track data to timeline-editor format
const convertTracksToTimelineData = (tracks: TimelineTrack[]): TimelineRow[] => {
  return tracks.map((track, index) => ({
    id: track.id,
    actions: track.clips.map((clip): TimelineAction => ({
      id: clip.id,
      start: clip.startTime,
      end: clip.startTime + clip.duration,
      effectId: `${track.type}-effect`,
      // Store additional clip data in flexible data field
      data: {
        type: clip.type,
        name: clip.name,
        source: clip.source,
        thumbnailUrl: clip.thumbnailUrl,
        trimStart: clip.trimStart,
        trimEnd: clip.trimEnd,
        volume: clip.volume,
        opacity: clip.opacity,
        effects: clip.effects,
        transitions: clip.transitions,
        metadata: clip.metadata,
      },
    })),
  }));
};

// Timeline effects for different track types
const timelineEffects: Record<string, TimelineEffect> = {
  "video-effect": {
    id: "video-effect",
    name: "Video",
    source: {
      start: ({ action, engine }) => (
        <div className="w-full h-full bg-blue-500/20 border border-blue-500 rounded flex items-center justify-center text-xs text-blue-700">
          <ForwardedIconComponent name="Video" className="w-3 h-3 mr-1" />
          {action.data?.name || "Video Clip"}
        </div>
      ),
    },
  },
  "audio-effect": {
    id: "audio-effect", 
    name: "Audio",
    source: {
      start: ({ action, engine }) => (
        <div className="w-full h-full bg-green-500/20 border border-green-500 rounded flex items-center justify-center text-xs text-green-700">
          <ForwardedIconComponent name="Volume2" className="w-3 h-3 mr-1" />
          {action.data?.name || "Audio Clip"}
        </div>
      ),
    },
  },
  "music-effect": {
    id: "music-effect",
    name: "Music", 
    source: {
      start: ({ action, engine }) => (
        <div className="w-full h-full bg-purple-500/20 border border-purple-500 rounded flex items-center justify-center text-xs text-purple-700">
          <ForwardedIconComponent name="Music" className="w-3 h-3 mr-1" />
          {action.data?.name || "Music Clip"}
        </div>
      ),
    },
  },
  "effects-effect": {
    id: "effects-effect",
    name: "Effects",
    source: {
      start: ({ action, engine }) => (
        <div className="w-full h-full bg-orange-500/20 border border-orange-500 rounded flex items-center justify-center text-xs text-orange-700">
          <ForwardedIconComponent name="Sparkles" className="w-3 h-3 mr-1" />
          {action.data?.name || "Effect"}
        </div>
      ),
    },
  },
  "titles-effect": {
    id: "titles-effect",
    name: "Titles",
    source: {
      start: ({ action, engine }) => (
        <div className="w-full h-full bg-yellow-500/20 border border-yellow-500 rounded flex items-center justify-center text-xs text-yellow-700">
          <ForwardedIconComponent name="Type" className="w-3 h-3 mr-1" />
          {action.data?.name || "Title"}
        </div>
      ),
    },
  },
};

/**
 * VideoTimeline - Multi-track timeline component for video editing
 * Leverages @xzdarcy/react-timeline-editor with custom video editing features
 */
export const VideoTimeline: React.FC<VideoTimelineProps> = ({
  state,
  onStateChange,
  onClipSelect,
  onClipMove,
  onClipTrim,
  onClipDelete,
  onTrackAdd,
  onTrackDelete,
  onPlaybackChange,
  onZoomChange,
  onTimeChange,
  disabled = false,
  className,
}) => {
  const timelineRef = useRef<TimelineEngine>(null);
  const [timelineData, setTimelineData] = useState<TimelineRow[]>(
    convertTracksToTimelineData(state.tracks)
  );

  // Convert tracks to timeline format when tracks change
  useEffect(() => {
    setTimelineData(convertTracksToTimelineData(state.tracks));
  }, [state.tracks]);

  // Handle timeline state changes
  const handleTimelineChange = useCallback((newState: LibTimelineState) => {
    // Convert timeline data back to our format
    const updatedTracks = state.tracks.map((track, index) => {
      const timelineRow = newState.data[index];
      if (!timelineRow) return track;

      const updatedClips = timelineRow.actions.map((action): TimelineClip => ({
        id: action.id,
        trackId: track.id,
        type: action.data?.type || "video",
        source: action.data?.source || "",
        thumbnailUrl: action.data?.thumbnailUrl,
        startTime: action.start,
        duration: action.end - action.start,
        trimStart: action.data?.trimStart || 0,
        trimEnd: action.data?.trimEnd || 0,
        name: action.data?.name || "Clip",
        metadata: action.data?.metadata,
        effects: action.data?.effects || [],
        transitions: action.data?.transitions || [],
        volume: action.data?.volume || 100,
        opacity: action.data?.opacity || 100,
        locked: false,
        selected: false,
      }));

      return {
        ...track,
        clips: updatedClips,
      };
    });

    // Update our state
    const newVideoState = {
      ...state,
      tracks: updatedTracks,
      view: {
        ...state.view,
        zoom: newState.scale || state.view.zoom,
        scrollX: newState.startLeft || state.view.scrollX,
      },
    };

    onStateChange?.(newVideoState);
    setTimelineData(newState.data);
  }, [state, onStateChange]);

  // Handle clip selection
  const handleActionSelect = useCallback((actionIds: string[]) => {
    onClipSelect?.(actionIds);
  }, [onClipSelect]);

  // Handle clip movement
  const handleActionMove = useCallback((actionId: string, start: number, end: number) => {
    // Find which track the clip moved to
    const clipData = timelineData.find(row => 
      row.actions.some(action => action.id === actionId)
    );
    
    if (clipData) {
      onClipMove?.(actionId, clipData.id, start);
    }
  }, [timelineData, onClipMove]);

  // Handle playhead time change
  const handleTimeUpdate = useCallback((time: number) => {
    onTimeChange?.(time);
  }, [onTimeChange]);

  // Playback controls
  const togglePlayback = useCallback(() => {
    const newPlayback: PlaybackState = {
      ...state.playback,
      playing: !state.playback.playing,
    };
    onPlaybackChange?.(newPlayback);
  }, [state.playback, onPlaybackChange]);

  const handleSeek = useCallback((time: number) => {
    const newPlayback: PlaybackState = {
      ...state.playback,
      currentTime: time,
    };
    onPlaybackChange?.(newPlayback);
  }, [state.playback, onPlaybackChange]);

  // Add track helper
  const handleAddTrack = useCallback((type: TrackType) => {
    onTrackAdd?.(type);
  }, [onTrackAdd]);

  return (
    <Card className={cn("flex flex-col h-full", className)}>
      {/* Timeline Controls Header */}
      <div className="flex items-center justify-between p-3 border-b border-border">
        <div className="flex items-center gap-2">
          {/* Playback Controls */}
          <Button
            variant="outline"
            size="sm"
            onClick={togglePlayback}
            disabled={disabled}
          >
            <ForwardedIconComponent 
              name={state.playback.playing ? "Pause" : "Play"} 
              className="w-4 h-4"
            />
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleSeek(0)}
            disabled={disabled}
          >
            <ForwardedIconComponent name="SkipBack" className="w-4 h-4" />
          </Button>

          <Separator orientation="vertical" className="h-6" />

          {/* Timeline Info */}
          <Label className="text-xs text-muted-foreground">
            {Math.floor(state.playback.currentTime / 60)}:
            {Math.floor(state.playback.currentTime % 60).toString().padStart(2, '0')} / 
            {Math.floor(state.playback.duration / 60)}:
            {Math.floor(state.playback.duration % 60).toString().padStart(2, '0')}
          </Label>
        </div>

        <div className="flex items-center gap-2">
          {/* Add Track Buttons */}
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleAddTrack("video")}
            disabled={disabled}
          >
            <ForwardedIconComponent name="Video" className="w-4 h-4 mr-1" />
            Video
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleAddTrack("audio")}
            disabled={disabled}
          >
            <ForwardedIconComponent name="Volume2" className="w-4 h-4 mr-1" />
            Audio
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={() => handleAddTrack("music")}
            disabled={disabled}
          >
            <ForwardedIconComponent name="Music" className="w-4 h-4 mr-1" />
            Music
          </Button>
        </div>
      </div>

      {/* Main Timeline Component */}
      <div className="flex-1 relative">
        <Timeline
          ref={timelineRef}
          onChange={handleTimelineChange}
          editorData={timelineData}
          effects={timelineEffects}
          onActionSelect={handleActionSelect}
          onActionMove={handleActionMove}
          onDoubleClickAction={(actionId) => {
            // Handle clip double-click for detailed editing
            console.log("Double-clicked clip:", actionId);
          }}
          onClickTime={handleTimeUpdate}
          autoScroll={true}
          dragLine={true}
          hideCursor={false}
          disableDrag={disabled}
          scale={state.view.zoom}
          scaleSplitCount={10}
          scaleWidth={160}
          startLeft={state.view.scrollX}
          className="w-full h-full"
          style={{
            "--timeline-primary-color": "hsl(var(--primary))",
            "--timeline-background-color": "hsl(var(--background))",
            "--timeline-border-color": "hsl(var(--border))",
          } as React.CSSProperties}
        />

        {/* Playhead Indicator */}
        <div 
          className="absolute top-0 bottom-0 w-0.5 bg-primary z-20 pointer-events-none"
          style={{
            left: `${(state.playback.currentTime * state.view.zoom) + 160}px`, // 160px for track headers
            transform: "translateX(-50%)",
          }}
        >
          {/* Playhead handle */}
          <div className="absolute -top-2 -left-2 w-4 h-4 bg-primary rounded-full border-2 border-background shadow-lg" />
        </div>
      </div>

      {/* Timeline Status Bar */}
      <div className="flex items-center justify-between p-2 border-t border-border bg-muted/50 text-xs text-muted-foreground">
        <div className="flex items-center gap-4">
          <span>Zoom: {Math.round(state.view.zoom)}px/s</span>
          <span>Tracks: {state.tracks.length}</span>
          <span>
            Clips: {state.tracks.reduce((total, track) => total + track.clips.length, 0)}
          </span>
        </div>
        
        <div className="flex items-center gap-2">
          {state.view.snapToGrid && (
            <span>Grid: {state.view.gridSize}s</span>
          )}
          {state.selection.selectedClips.length > 0 && (
            <span>Selected: {state.selection.selectedClips.length}</span>
          )}
        </div>
      </div>
    </Card>
  );
};

export default VideoTimeline;
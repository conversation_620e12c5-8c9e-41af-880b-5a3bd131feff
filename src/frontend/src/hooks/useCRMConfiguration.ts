import { useMemo } from 'react';
import { useWorkspaceConfiguration } from './useWorkspaceConfiguration';

// Type definitions for CRM-specific configurations
export interface SalesPipelineStage {
  id: string;
  name: string;
  color: string;
  description?: string;
  auto_move_days?: number;
  required_fields?: string[];
}

export interface SalesPipelineConfig {
  stages: SalesPipelineStage[];
  stage_transitions: Record<string, string[]>;
  stage_requirements?: Record<string, string[]>;
}

export interface CustomerFieldConfig {
  required_fields: string[];
  custom_fields: Array<{
    id: string;
    name: string;
    label: string;
    type: 'text' | 'number' | 'boolean' | 'date' | 'select' | 'multiselect';
    required: boolean;
    options?: string[];
    default_value?: any;
    validation_pattern?: string;
    description?: string;
    show_in_list?: boolean;
    show_in_detail?: boolean;
    searchable?: boolean;
  }>;
  field_visibility?: Record<string, string[]>;
}

export interface NoteTemplateConfig {
  categories: Array<{
    id: string;
    name: string;
    icon: string;
    color: string;
  }>;
  templates: Record<string, Array<{
    id: string;
    name: string;
    category_id: string;
    content: string;
    variables?: string[];
  }>>;
}

export interface UIPreferencesConfig {
  theme: {
    primary_color?: string;
    secondary_color?: string;
    dark_mode?: boolean;
  };
  layout: {
    sidebar_collapsed?: boolean;
    default_view?: 'list' | 'grid' | 'kanban';
    items_per_page?: number;
  };
  dashboard_widgets?: Array<{
    id: string;
    type: string;
    position: { x: number; y: number; w: number; h: number };
    config?: any;
  }>;
}

export interface IntegrationsConfig {
  email?: {
    provider?: string;
    enabled?: boolean;
    settings?: any;
  };
  calendar?: {
    provider?: string;
    enabled?: boolean;
    settings?: any;
  };
  third_party_apis?: Record<string, any>;
}

interface UseCRMConfigurationReturn {
  // Configuration values
  salesPipeline: SalesPipelineConfig;
  customerFields: CustomerFieldConfig;
  noteTemplates: NoteTemplateConfig;
  uiPreferences: UIPreferencesConfig;
  integrations: IntegrationsConfig;
  
  // Helper functions
  getStageName: (stageId: string) => string;
  getStageColor: (stageId: string) => string;
  getNextStage: (currentStageId: string) => string | null;
  isFieldRequired: (fieldId: string) => boolean;
  getCustomFieldConfig: (fieldId: string) => any;
  getNoteTemplatesByCategory: (categoryId: string) => any[];
  getThemeColor: (colorKey: string) => string;
  isIntegrationEnabled: (integration: string) => boolean;
  
  // Loading and error states
  loading: boolean;
  error: string | null;
}

// Default configurations
const DEFAULT_SALES_PIPELINE: SalesPipelineConfig = {
  stages: [
    { id: 'lead', name: 'Lead', color: '#6B7280' },
    { id: 'qualified', name: 'Qualified', color: '#3B82F6' },
    { id: 'proposal', name: 'Proposal', color: '#F59E0B' },
    { id: 'negotiation', name: 'Negotiation', color: '#8B5CF6' },
    { id: 'closed_won', name: 'Closed Won', color: '#10B981' },
    { id: 'closed_lost', name: 'Closed Lost', color: '#EF4444' },
  ],
  stage_transitions: {
    lead: ['qualified', 'closed_lost'],
    qualified: ['proposal', 'closed_lost'],
    proposal: ['negotiation', 'closed_lost'],
    negotiation: ['closed_won', 'closed_lost'],
  },
};

const DEFAULT_CUSTOMER_FIELDS: CustomerFieldConfig = {
  required_fields: ['name', 'email'],
  custom_fields: [],
};

const DEFAULT_NOTE_TEMPLATES: NoteTemplateConfig = {
  categories: [
    { id: 'meeting', name: 'Meeting Notes', icon: 'users', color: '#3B82F6' },
    { id: 'call', name: 'Call Notes', icon: 'phone', color: '#10B981' },
  ],
  templates: {},
};

const DEFAULT_UI_PREFERENCES: UIPreferencesConfig = {
  theme: {
    dark_mode: false,
  },
  layout: {
    sidebar_collapsed: false,
    default_view: 'list',
    items_per_page: 25,
  },
};

const DEFAULT_INTEGRATIONS: IntegrationsConfig = {};

export const useCRMConfiguration = (): UseCRMConfigurationReturn => {
  const { getConfigValue, loading, error } = useWorkspaceConfiguration();

  // Get configuration values with defaults and validation
  const rawSalesPipeline = getConfigValue<SalesPipelineConfig>('sales_pipeline', DEFAULT_SALES_PIPELINE);
  const salesPipeline: SalesPipelineConfig = {
    stages: Array.isArray(rawSalesPipeline?.stages) ? rawSalesPipeline.stages : DEFAULT_SALES_PIPELINE.stages,
    stage_transitions: rawSalesPipeline?.stage_transitions || DEFAULT_SALES_PIPELINE.stage_transitions,
    stage_requirements: rawSalesPipeline?.stage_requirements || {},
  };
  
  const rawCustomerFields = getConfigValue<CustomerFieldConfig>('customer_fields', DEFAULT_CUSTOMER_FIELDS);
  const customerFields: CustomerFieldConfig = {
    required_fields: Array.isArray(rawCustomerFields?.required_fields) ? rawCustomerFields.required_fields : DEFAULT_CUSTOMER_FIELDS.required_fields,
    custom_fields: Array.isArray(rawCustomerFields?.custom_fields) ? rawCustomerFields.custom_fields : DEFAULT_CUSTOMER_FIELDS.custom_fields,
    field_visibility: rawCustomerFields?.field_visibility || {},
  };
  
  const noteTemplates = getConfigValue<NoteTemplateConfig>('note_templates', DEFAULT_NOTE_TEMPLATES);
  const uiPreferences = getConfigValue<UIPreferencesConfig>('ui_preferences', DEFAULT_UI_PREFERENCES);
  const integrations = getConfigValue<IntegrationsConfig>('integrations', DEFAULT_INTEGRATIONS);

  // Memoized helper functions
  const helpers = useMemo(() => ({
    getStageName: (stageId: string): string => {
      const stage = salesPipeline?.stages?.find(s => s.id === stageId);
      return stage?.name || stageId;
    },
    
    getStageColor: (stageId: string): string => {
      const stage = salesPipeline?.stages?.find(s => s.id === stageId);
      return stage?.color || '#6B7280';
    },
    
    getNextStage: (currentStageId: string): string | null => {
      const transitions = salesPipeline?.stage_transitions?.[currentStageId];
      return transitions?.[0] || null;
    },
    
    isFieldRequired: (fieldId: string): boolean => {
      return customerFields?.required_fields?.includes(fieldId) || false;
    },
    
    getCustomFieldConfig: (fieldId: string): any => {
      return customerFields?.custom_fields?.find(f => f.id === fieldId);
    },
    
    getNoteTemplatesByCategory: (categoryId: string): any[] => {
      return noteTemplates?.templates?.[categoryId] || [];
    },
    
    getThemeColor: (colorKey: string): string => {
      const colors: Record<string, string> = {
        primary: uiPreferences.theme.primary_color || '#3B82F6',
        secondary: uiPreferences.theme.secondary_color || '#6B7280',
      };
      return colors[colorKey] || '#6B7280';
    },
    
    isIntegrationEnabled: (integration: string): boolean => {
      const config = integrations[integration as keyof IntegrationsConfig];
      return config?.enabled || false;
    },
  }), [salesPipeline, customerFields, noteTemplates, uiPreferences, integrations]);

  return {
    // Configuration values
    salesPipeline,
    customerFields,
    noteTemplates,
    uiPreferences,
    integrations,
    
    // Helper functions
    ...helpers,
    
    // Loading and error states
    loading,
    error,
  };
};
/**
 * Task Management Types - LEVER compliant
 */

export interface TaskBoard {
  id: string;
  name: string;
  description?: string;
  folder_id?: string;
  created_at: string;
  updated_at: string;
  columns: TaskColumn[];
}

export interface TaskColumn {
  id: string;
  title: string;
  position: number;
  board_id?: string;
  tasks: Task[];
}

export interface Task {
  id: string;
  title: string;
  description?: string;
  status: string;
  priority: "low" | "medium" | "high";
  column_id: string;
  position: number;
  flow_id?: string;
  user_id?: string;
  created_at: string;
  updated_at: string;
}

export interface TaskBoardCreate {
  name: string;
  description?: string;
  folder_id?: string;
}

export interface TaskCreate {
  title: string;
  description?: string;
  flow_id?: string;
}

export interface TaskMove {
  column_id: string;
  position: number;
}
